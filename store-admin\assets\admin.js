/**
 * Store Admin JavaScript
 * Handles all client-side functionality for the admin interface
 */

// Global variables
let currentEditId = null;
let currentPage = 1;
let currentSearch = '';
let currentPerPage = 20;

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Loading overlay functions
function showLoadingOverlay(text = 'Loading...') {
    const overlay = document.getElementById('loading-overlay');
    const loadingText = overlay?.querySelector('.loading-text');
    if (overlay) {
        if (loadingText) {
            loadingText.textContent = text;
        }
        overlay.classList.add('show');
    }
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

// Main initialization
document.addEventListener('DOMContentLoaded', function() {
    // Hide loading overlay when page is ready
    hideLoadingOverlay();

    initializeUI();
    initializeViewToggle();
    initializeSearch();
    initializeFilters();
    initializeReordering();
    initializeToolbarActions();
    initializeSidebar();
    initializeTooltips();
    initializeDropdowns();

    initializeFormValidation();

    initializeTouchEnhancements();
});

// Touch enhancements for mobile devices
function initializeTouchEnhancements() {
    // Add touch-friendly classes for mobile devices
    if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
        document.body.classList.add('touch-device');

        // Simple touch feedback for buttons only
        const buttons = document.querySelectorAll('.btn, .btn-icon, .nav-link');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function() {
                this.classList.add('touch-active');
            }, { passive: true });

            button.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.classList.remove('touch-active');
                }, 150);
            }, { passive: true });
        });
    }
}

// UI Components
function initializeUI() {
    // Entity selection functionality removed - no longer needed

    // Initialize card interactions
    const entityCards = document.querySelectorAll('.entity-card:not(.add-card)');
    entityCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(0)';
            }
        });
    });
}

// Bulk Actions Management - Removed since checkboxes are no longer used

// Simplified View Toggle - Cards First, Tables Only on Large Screens
function initializeViewToggle() {
    const viewToggleButtons = document.querySelectorAll('.view-toggle button');

    // Only initialize if view toggle exists (large screens only)
    if (viewToggleButtons.length === 0) {
        return;
    }

    // Set up click handlers
    viewToggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            viewToggleButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            const viewType = this.getAttribute('data-view');
            toggleView(viewType);
        });
    });

    // Initialize with default view
    initializeDefaultView();
}

function initializeDefaultView() {
    // Check if we're on a large screen where tables are available
    const isLargeScreen = window.matchMedia('(min-width: 1200px)').matches;
    const grid = document.querySelector('.entity-grid');

    if (!isLargeScreen) {
        // Force cards view on smaller screens
        setActiveView('cards');
        return;
    }

    // On large screens, check for saved preference or default to table
    const savedPreference = localStorage.getItem('admin-view-preference');
    const defaultView = savedPreference || 'table'; // Default to table view on large screens

    // If defaulting to table view, hide cards initially to prevent flash
    if (defaultView === 'table' && grid) {
        grid.style.display = 'none';
    }

    setActiveView(defaultView);
}

function getResponsiveDefaultView() {
    // Default to table on large screens, cards on small screens
    const isLargeScreen = window.matchMedia('(min-width: 1200px)').matches;
    return isLargeScreen ? 'table' : 'cards';
}

function setActiveView(viewType) {
    // Update button states
    const viewToggleButtons = document.querySelectorAll('.view-toggle button');
    viewToggleButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-view') === viewType) {
            btn.classList.add('active');
        }
    });

    // Apply the view
    toggleView(viewType);
}

function toggleView(viewType) {
    const grid = document.querySelector('.entity-grid');
    const tableContainer = document.querySelector('.table-container');

    if (!grid) return;

    // Add loading state to the main container
    const mainContainer = grid.parentElement;
    if (mainContainer) {
        mainContainer.classList.add('view-loading');
    }

    // Use setTimeout to allow loading state to show
    setTimeout(() => {
        if (viewType === 'table' && window.innerWidth >= 1200) {
            // Only allow table view on large screens
            grid.style.display = 'none';
            if (tableContainer) {
                tableContainer.style.display = 'block';
            } else {
                // Generate table if it doesn't exist
                generateTableView();
            }
        } else {
            // Default to cards view
            grid.style.display = 'grid';
            if (tableContainer) {
                tableContainer.style.display = 'none';
            }
        }

        // Remove loading state after transition
        setTimeout(() => {
            if (mainContainer) {
                mainContainer.classList.remove('view-loading');
            }
        }, 100);

        // Save user preference only for large screens
        if (window.innerWidth >= 1200) {
            localStorage.setItem('admin-view-preference', viewType);
        }
    }, 50);
}

// Simplified responsive behavior
function handleResponsiveViewChange() {
    const isLargeScreen = window.matchMedia('(min-width: 1200px)').matches;

    if (!isLargeScreen) {
        // Force cards view on smaller screens
        setActiveView('cards');
        // Clear any table preference since tables aren't available
        localStorage.removeItem('admin-view-preference');
    } else {
        // On large screens, restore preference or default to table
        const savedPreference = localStorage.getItem('admin-view-preference');
        setActiveView(savedPreference || 'table');
    }
}

// Add resize listener with debouncing
window.addEventListener('resize', debounce(handleResponsiveViewChange, 250));

// Universal entity view function for clickable rows and cards
function viewEntity(id, type) {
    // Show loading overlay
    showLoadingOverlay('Loading details...');

    const baseUrl = '/store-admin/';
    const viewPages = {
        'customer': 'view-customer',
        'service': 'view-service',
        'employee': 'view-employee',
        'category': 'view-category',
        'reservation': 'view-reservation'
    };

    const page = viewPages[type] || 'view-' + type;
    window.location.href = `${baseUrl}?page=${page}&id=${id}`;
}

// Column configuration for each entity type
const ENTITY_COLUMNS = {
    customers: {
        essential: ['name', 'email', 'phone', 'visits', 'status'],
        optional: ['language', 'total_spent', 'last_visit', 'notes']
    },
    services: {
        essential: ['name', 'category', 'duration', 'price', 'status'],
        optional: ['description', 'popularity', 'created', 'employee_selection']
    },
    employees: {
        essential: ['name', 'position', 'email', 'phone', 'services', 'status'],
        optional: ['schedule', 'performance', 'hired', 'rating']
    },
    categories: {
        essential: ['name', 'icon', 'services_count', 'status'],
        optional: ['description', 'revenue', 'popularity', 'created']
    },
    reservations: {
        essential: ['customer', 'service', 'date', 'time', 'status'],
        optional: ['booking_id', 'employee', 'duration', 'price', 'payment', 'created']
    }
};

// Get visible columns for entity type
function getVisibleColumns(entityType) {
    const config = ENTITY_COLUMNS[entityType];
    if (!config) return [];

    const storageKey = `table_columns_${entityType}`;
    const saved = localStorage.getItem(storageKey);

    if (saved) {
        try {
            const parsed = JSON.parse(saved);
            return [...config.essential, ...parsed.optional];
        } catch (e) {
            // Fall back to essential columns if parsing fails
        }
    }

    return config.essential;
}

// Save column preferences
function saveColumnPreferences(entityType, optionalColumns) {
    const storageKey = `table_columns_${entityType}`;
    localStorage.setItem(storageKey, JSON.stringify({ optional: optionalColumns }));
}

// Initialize dropdown functionality
function initializeDropdowns() {
    document.addEventListener('click', function(e) {
        // Close all dropdowns when clicking outside
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown').forEach(dropdown => {
                dropdown.classList.remove('open');
            });
        }

        // Toggle dropdown when clicking toggle button
        if (e.target.closest('.dropdown-toggle')) {
            e.preventDefault();
            const dropdown = e.target.closest('.dropdown');

            // Close other dropdowns
            document.querySelectorAll('.dropdown').forEach(d => {
                if (d !== dropdown) d.classList.remove('open');
            });

            dropdown.classList.toggle('open');
        }
    });
}

// Initialize column selector functionality
function initializeColumnSelector() {
    const toggle = document.querySelector('.column-selector-toggle');
    const dropdown = document.querySelector('.column-selector-dropdown');
    const checkboxes = document.querySelectorAll('.column-option.optional input[type="checkbox"]');

    if (!toggle || !dropdown) return;

    // Toggle dropdown
    toggle.addEventListener('click', (e) => {
        e.stopPropagation();
        dropdown.classList.toggle('show');
        toggle.classList.toggle('active');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.column-selector')) {
            dropdown.classList.remove('show');
            toggle.classList.remove('active');
        }
    });

    // Handle column checkbox changes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            const urlParams = new URLSearchParams(window.location.search);
            let currentPage = urlParams.get('page') || 'customers';

            // Map page names to entity types
            const pageToEntity = {
                'customers': 'customers',
                'services': 'services',
                'employees': 'employees',
                'categories': 'categories',
                'reservations': 'reservations'
            };

            const entityType = pageToEntity[currentPage] || currentPage;

            // Get currently selected optional columns
            const selectedOptional = Array.from(document.querySelectorAll('.column-option.optional input[type="checkbox"]'))
                .filter(cb => cb.checked)
                .map(cb => cb.dataset.column);

            // Save preferences
            saveColumnPreferences(entityType, selectedOptional);

            // Regenerate only the table part, not the column selector
            const tableContainer = document.querySelector('.table-container');
            const existingTable = tableContainer?.querySelector('.table');
            if (tableContainer && existingTable) {
                const tableHTML = buildTableFromCards();
                // Replace only the table, keep the column selector
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = tableHTML;
                const newTable = tempDiv.querySelector('.table');
                if (newTable) {
                    existingTable.replaceWith(newTable);
                }
            }
        });
    });
}

function generateTableView() {
    // Only generate table if we're on a large screen
    if (window.innerWidth < 1200) {
        return;
    }

    let tableContainer = document.querySelector('.table-container');
    if (!tableContainer) {
        tableContainer = document.createElement('div');
        tableContainer.className = 'table-container';

        // Add column selector and table
        const columnSelector = generateColumnSelector();
        const tableHTML = buildTableFromCards();
        tableContainer.innerHTML = columnSelector + tableHTML;

        // Insert after the entity grid
        const grid = document.querySelector('.entity-grid');
        if (grid && grid.parentNode) {
            grid.parentNode.insertBefore(tableContainer, grid.nextSibling);
        }

        // Initialize column selector only once when creating the container
        initializeColumnSelector();
    } else {
        // If container exists, just regenerate the table content
        const existingTable = tableContainer.querySelector('.table');
        if (existingTable) {
            const tableHTML = buildTableFromCards();
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = tableHTML;
            const newTable = tempDiv.querySelector('.table');
            if (newTable) {
                existingTable.replaceWith(newTable);
            }
        }
    }

    tableContainer.style.display = 'block';
}

// Generate column selector dropdown
function generateColumnSelector() {
    const urlParams = new URLSearchParams(window.location.search);
    let currentPage = urlParams.get('page') || window.location.pathname.split('/').pop().replace('.php', '') || 'dashboard';

    // Map page names to entity types
    const pageToEntity = {
        'customers': 'customers',
        'services': 'services',
        'employees': 'employees',
        'categories': 'categories',
        'reservations': 'reservations'
    };

    const entityType = pageToEntity[currentPage] || currentPage;
    const config = ENTITY_COLUMNS[entityType];
    if (!config) return '';

    const visibleColumns = getVisibleColumns(entityType);

    let html = `
        <div class="table-controls">
            <div class="column-selector">
                <button class="btn btn-outline column-selector-toggle" type="button">
                    <i class="fas fa-columns"></i> Columns
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="column-selector-dropdown">
                    <div class="column-selector-header">
                        <strong>Essential Columns</strong>
                        <small>Always visible</small>
                    </div>`;

    config.essential.forEach(col => {
        const label = col.charAt(0).toUpperCase() + col.slice(1).replace('_', ' ');
        html += `
                    <div class="column-option essential">
                        <input type="checkbox" id="col-${col}" checked disabled>
                        <label for="col-${col}">${label}</label>
                    </div>`;
    });

    html += `
                    <div class="column-selector-header">
                        <strong>Optional Columns</strong>
                        <small>Choose additional columns</small>
                    </div>`;

    config.optional.forEach(col => {
        const label = col.charAt(0).toUpperCase() + col.slice(1).replace('_', ' ');
        const checked = visibleColumns.includes(col) ? 'checked' : '';
        html += `
                    <div class="column-option optional">
                        <input type="checkbox" id="col-${col}" ${checked} data-column="${col}">
                        <label for="col-${col}">${label}</label>
                    </div>`;
    });

    html += `
                </div>
            </div>
        </div>`;

    return html;
}

function buildTableFromCards() {
    const cards = document.querySelectorAll('.entity-card:not(.add-card)');
    if (cards.length === 0) {
        return '<div class="empty-state"><p>No data to display in table view</p></div>';
    }

    // Determine table type based on current page URL
    const urlParams = new URLSearchParams(window.location.search);
    const currentPage = urlParams.get('page') || window.location.pathname.split('/').pop().replace('.php', '') || 'dashboard';

    let tableHTML = '<table class="table">';

    // Generate appropriate table based on page
    switch(currentPage) {
        case 'customers':
            tableHTML += generateCustomersTable(cards);
            break;
        case 'services':
            tableHTML += generateServicesTable(cards);
            break;
        case 'employees':
            tableHTML += generateEmployeesTable(cards);
            break;
        case 'categories':
            tableHTML += generateCategoriesTable(cards);
            break;
        case 'reservations':
            tableHTML += generateReservationsTable(cards);
            break;
        default:
            tableHTML += generateGenericTable(cards);
    }

    tableHTML += '</table>';
    return tableHTML;
}

function generateCustomersTable(cards) {
    const visibleColumns = getVisibleColumns('customers');
    const columnLabels = {
        name: 'Name',
        email: 'Email',
        phone: 'Phone',
        language: 'Language',
        visits: 'Visits',
        total_spent: 'Total Spent',
        last_visit: 'Last Visit',
        status: 'Status',
        notes: 'Notes'
    };

    let html = `
        <thead>
            <tr>`;

    visibleColumns.forEach(col => {
        html += `<th>${columnLabels[col] || col}</th>`;
    });

    html += `
            </tr>
        </thead>
        <tbody>
    `;

    cards.forEach(card => {
        const id = card.getAttribute('data-id');

        // Extract all possible data from card
        const data = {
            name: card.querySelector('.entity-title')?.textContent || 'Unknown',
            email: card.querySelector('a[href^="mailto:"]')?.textContent.trim() || '-',
            phone: card.querySelector('a[href^="tel:"]')?.textContent.trim() || '-',
            language: card.querySelector('.language-badge')?.textContent.trim() || 'EL',
            visits: card.querySelectorAll('.stat-item')[0]?.querySelector('.stat-value')?.textContent || '0',
            total_spent: card.querySelectorAll('.stat-item')[1]?.querySelector('.stat-value')?.textContent || '€0',
            last_visit: card.querySelectorAll('.stat-item')[2]?.querySelector('.stat-value')?.textContent || 'Never',
            notes: card.querySelector('.entity-notes')?.textContent.trim() || '-'
        };

        // Determine status from card classes
        let status = 'New';
        let statusClass = 'status-inactive';
        if (card.classList.contains('entity-card--active')) {
            status = 'Active';
            statusClass = 'status-active';
        } else if (card.classList.contains('entity-card--inactive')) {
            status = 'Inactive';
            statusClass = 'status-inactive';
        }
        data.status = status;

        html += `
            <tr class="table-row-clickable" data-id="${id}" data-type="customer" onclick="viewEntity('${id}', 'customer')">`;

        visibleColumns.forEach(col => {
            let cellContent = data[col] || '-';

            // Apply special formatting for certain columns
            if (col === 'name') {
                cellContent = `<strong>${cellContent}</strong>`;
            } else if (col === 'email') {
                cellContent = `<a href="mailto:${cellContent}" class="contact-link" onclick="event.stopPropagation()">${cellContent}</a>`;
            } else if (col === 'phone') {
                cellContent = `<a href="tel:${cellContent}" class="contact-link" onclick="event.stopPropagation()">${cellContent}</a>`;
            } else if (col === 'language') {
                cellContent = `<span class="language-badge">${cellContent}</span>`;
            } else if (col === 'visits') {
                cellContent = `<span class="stat-highlight">${cellContent}</span>`;
            } else if (col === 'total_spent') {
                cellContent = `<span class="price-highlight">${cellContent}</span>`;
            } else if (col === 'status') {
                cellContent = `<span class="status-badge ${statusClass}">${cellContent}</span>`;
            }

            html += `<td>${cellContent}</td>`;
        });

        html += `</tr>`;
    });

    html += '</tbody>';
    return html;
}

function generateServicesTable(cards) {
    const visibleColumns = getVisibleColumns('services');
    const columnLabels = {
        name: 'Service Name',
        category: 'Category',
        description: 'Description',
        duration: 'Duration',
        price: 'Price',
        popularity: 'Popularity',
        status: 'Status',
        created: 'Created',
        employee_selection: 'Employee Selection'
    };

    let html = `
        <thead>
            <tr>`;

    visibleColumns.forEach(col => {
        html += `<th>${columnLabels[col] || col}</th>`;
    });

    html += `
            </tr>
        </thead>
        <tbody>
    `;

    cards.forEach(card => {
        const id = card.getAttribute('data-id');

        // Extract all possible data from card
        const data = {
            name: card.querySelector('.entity-title')?.textContent || 'Unknown Service',
            category: card.querySelector('.category-badge')?.textContent.trim() || 'Uncategorized',
            description: card.querySelector('.info-content .info-value')?.textContent.trim().substring(0, 50) + '...' || '-',
            duration: card.querySelector('.duration-badge')?.textContent.trim() ||
                     card.querySelectorAll('.stat-item')[1]?.querySelector('.stat-value')?.textContent || '-',
            price: card.querySelector('.price-badge')?.textContent.trim() ||
                  card.querySelectorAll('.stat-item')[0]?.querySelector('.stat-value')?.textContent || '-',
            popularity: 'Standard',
            created: new Date().toLocaleDateString(),
            employee_selection: 'Auto'
        };

        // Determine status from card classes
        let status = 'Active';
        let statusClass = 'status-active';
        if (card.classList.contains('entity-card--inactive')) {
            status = 'Inactive';
            statusClass = 'status-inactive';
        }
        data.status = status;

        html += `
            <tr class="table-row-clickable" data-id="${id}" data-type="service" onclick="viewEntity('${id}', 'service')">`;

        visibleColumns.forEach(col => {
            let cellContent = data[col] || '-';

            // Apply special formatting for certain columns
            if (col === 'name') {
                cellContent = `<strong>${cellContent}</strong>`;
            } else if (col === 'category') {
                cellContent = `<span class="category-badge">${cellContent}</span>`;
            } else if (col === 'duration') {
                cellContent = `<span class="duration-badge">${cellContent}</span>`;
            } else if (col === 'price') {
                cellContent = `<span class="price-highlight">${cellContent}</span>`;
            } else if (col === 'popularity') {
                cellContent = `<span class="popularity-badge">${cellContent}</span>`;
            } else if (col === 'status') {
                cellContent = `<span class="status-badge ${statusClass}">${cellContent}</span>`;
            } else if (col === 'description') {
                cellContent = `<span class="description-text">${cellContent}</span>`;
            }

            html += `<td>${cellContent}</td>`;
        });

        html += `</tr>`;
    });

    html += '</tbody>';
    return html;
}

function generateEmployeesTable(cards) {
    const visibleColumns = getVisibleColumns('employees');
    const columnLabels = {
        name: 'Name',
        position: 'Position',
        email: 'Email',
        phone: 'Phone',
        services: 'Services',
        schedule: 'Schedule',
        performance: 'Performance',
        status: 'Status',
        hired: 'Hired',
        rating: 'Rating'
    };

    let html = `
        <thead>
            <tr>`;

    visibleColumns.forEach(col => {
        html += `<th>${columnLabels[col] || col}</th>`;
    });

    html += `
            </tr>
        </thead>
        <tbody>
    `;

    cards.forEach(card => {
        const id = card.getAttribute('data-id');

        // Extract all possible data from card
        const data = {
            name: card.querySelector('.entity-title')?.textContent || 'Unknown',
            position: card.querySelector('.status-badge')?.textContent.trim() || 'Staff Member',
            email: card.querySelector('.info-row .info-icon.email')?.parentElement.querySelector('.info-value')?.textContent.trim() || '-',
            phone: card.querySelector('.info-row .info-icon.phone')?.parentElement.querySelector('.info-value')?.textContent.trim() || '-',
            services: card.querySelector('.stat-item:nth-child(1) .stat-value')?.textContent || '0',
            schedule: card.querySelector('.stat-item:nth-child(2) .stat-value')?.textContent || 'Not Set',
            performance: card.querySelector('.stat-item:nth-child(3) .stat-value')?.textContent || 'N/A',
            hired: card.querySelector('.entity-footer')?.textContent.replace('Hired:', '').trim() || '-',
            rating: '4.8' // Could be from actual data
        };

        // Extract status from status indicator
        const statusIndicator = card.querySelector('.entity-status-indicator');
        let status = 'Inactive';
        let statusClass = 'status-inactive';
        if (statusIndicator?.classList.contains('active')) {
            status = 'Active';
            statusClass = 'status-active';
        }
        data.status = status;

        html += `
            <tr class="table-row-clickable" data-id="${id}" data-type="employee" onclick="viewEntity('${id}', 'employee')">`;

        visibleColumns.forEach(col => {
            let cellContent = data[col] || '-';

            // Apply special formatting for certain columns
            if (col === 'name') {
                cellContent = `<strong>${cellContent}</strong>`;
            } else if (col === 'position') {
                cellContent = `<span class="position-badge">${cellContent}</span>`;
            } else if (col === 'email') {
                cellContent = `<a href="mailto:${cellContent}" class="contact-link" onclick="event.stopPropagation()">${cellContent}</a>`;
            } else if (col === 'phone') {
                cellContent = `<a href="tel:${cellContent}" class="contact-link" onclick="event.stopPropagation()">${cellContent}</a>`;
            } else if (col === 'services') {
                cellContent = `<span class="stat-highlight">${cellContent} services</span>`;
            } else if (col === 'performance') {
                cellContent = `<span class="performance-badge">${cellContent}</span>`;
            } else if (col === 'status') {
                cellContent = `<span class="status-badge ${statusClass}">${cellContent}</span>`;
            }

            html += `<td>${cellContent}</td>`;
        });

        html += `</tr>`;
    });

    html += '</tbody>';
    return html;
}

function generateCategoriesTable(cards) {
    const visibleColumns = getVisibleColumns('categories');
    const columnLabels = {
        name: 'Category Name',
        icon: 'Icon',
        description: 'Description',
        services_count: 'Services Count',
        revenue: 'Total Revenue',
        popularity: 'Popularity',
        status: 'Status',
        created: 'Created'
    };

    let html = `
        <thead>
            <tr>`;

    visibleColumns.forEach(col => {
        html += `<th>${columnLabels[col] || col}</th>`;
    });

    html += `
            </tr>
        </thead>
        <tbody>
    `;

    cards.forEach(card => {
        const id = card.getAttribute('data-id');

        // Extract all possible data from card
        const iconElement = card.querySelector('.category-icon i');
        const data = {
            name: card.querySelector('.entity-title')?.textContent || 'Unknown',
            icon: iconElement ? `<i class="${iconElement.className}" style="color: ${card.querySelector('.category-icon').style.backgroundColor}"></i>` : '<i class="fas fa-tag"></i>',
            description: card.querySelector('.info-row .info-icon.description')?.parentElement.querySelector('.info-value')?.textContent.trim() || '-',
            services_count: card.querySelector('.stat-item:nth-child(1) .stat-value')?.textContent || '0',
            revenue: card.querySelector('.stat-item:nth-child(2) .stat-value')?.textContent || '€0',
            popularity: card.querySelector('.stat-item:nth-child(3) .stat-value')?.textContent || 'New',
            created: card.querySelector('.entity-footer')?.textContent.replace('Created:', '').trim() || '-'
        };

        // Extract status from status indicator
        const statusIndicator = card.querySelector('.entity-status-indicator');
        let status = 'Inactive';
        let statusClass = 'status-inactive';
        if (statusIndicator?.classList.contains('active')) {
            status = 'Active';
            statusClass = 'status-active';
        }
        data.status = status;

        html += `
            <tr class="table-row-clickable" data-id="${id}" data-type="category" onclick="viewEntity('${id}', 'category')">`;

        visibleColumns.forEach(col => {
            let cellContent = data[col] || '-';

            // Apply special formatting for certain columns
            if (col === 'name') {
                cellContent = `<strong>${cellContent}</strong>`;
            } else if (col === 'icon') {
                cellContent = `<span class="icon-cell">${cellContent}</span>`;
            } else if (col === 'services_count') {
                cellContent = `<span class="stat-highlight">${cellContent}</span>`;
            } else if (col === 'revenue') {
                cellContent = `<span class="price-highlight">${cellContent}</span>`;
            } else if (col === 'popularity') {
                cellContent = `<span class="popularity-badge">${cellContent}</span>`;
            } else if (col === 'status') {
                cellContent = `<span class="status-badge ${statusClass}">${cellContent}</span>`;
            } else if (col === 'description') {
                cellContent = `<span class="description-text">${cellContent}</span>`;
            }

            html += `<td>${cellContent}</td>`;
        });

        html += `</tr>`;
    });

    html += '</tbody>';
    return html;
}

function generateReservationsTable(cards) {
    const visibleColumns = getVisibleColumns('reservations');
    const columnLabels = {
        booking_id: 'Booking ID',
        customer: 'Customer',
        service: 'Service',
        employee: 'Employee',
        date: 'Date',
        time: 'Time',
        duration: 'Duration',
        price: 'Price',
        status: 'Status',
        payment: 'Payment',
        created: 'Created'
    };

    let html = `
        <thead>
            <tr>`;

    visibleColumns.forEach(col => {
        html += `<th>${columnLabels[col] || col}</th>`;
    });

    html += `
            </tr>
        </thead>
        <tbody>
    `;

    cards.forEach(card => {
        const id = card.getAttribute('data-id');

        // Extract all possible data from card
        const infoRows = card.querySelectorAll('.info-row');
        let service = '-';
        let employee = 'Not assigned';

        for (let row of infoRows) {
            const label = row.querySelector('.info-label');
            if (label && label.textContent.includes('Service')) {
                service = row.querySelector('.info-value').textContent.trim();
            }
            if (label && label.textContent.includes('Employee')) {
                employee = row.querySelector('.info-value').textContent.trim();
            }
        }

        const data = {
            booking_id: card.getAttribute('data-booking-id') || `RSV${id.slice(-6)}`,
            customer: card.querySelector('.entity-title')?.textContent || 'Unknown',
            service: service,
            employee: employee,
            date: card.querySelector('.date-value')?.textContent.trim() || '-',
            time: card.querySelector('.time-value')?.textContent.trim() || '-',
            duration: card.querySelector('.stat-item:nth-child(2) .stat-value')?.textContent || '-',
            price: card.querySelector('.stat-item:nth-child(1) .stat-value')?.textContent || '-',
            payment: card.querySelector('.stat-item:nth-child(3) .stat-value')?.textContent || 'Pending',
            created: card.querySelector('.entity-footer')?.textContent.replace('Created:', '').trim() || '-'
        };

        // Extract status from status badge
        const statusBadge = card.querySelector('.status-badge');
        const status = statusBadge ? statusBadge.textContent.trim() : 'Pending';
        const statusClass = statusBadge ? statusBadge.className.split(' ').find(c => c.startsWith('status-')) : 'status-pending';
        data.status = status;

        html += `
            <tr class="table-row-clickable" data-id="${id}" data-type="reservation" onclick="viewEntity('${id}', 'reservation')">`;

        visibleColumns.forEach(col => {
            let cellContent = data[col] || '-';

            // Apply special formatting for certain columns
            if (col === 'customer') {
                cellContent = `<strong>${cellContent}</strong>`;
            } else if (col === 'service') {
                cellContent = `<span class="service-name">${cellContent}</span>`;
            } else if (col === 'employee') {
                cellContent = `<span class="employee-name">${cellContent}</span>`;
            } else if (col === 'date') {
                cellContent = `<span class="date-highlight">${cellContent}</span>`;
            } else if (col === 'time') {
                cellContent = `<span class="time-highlight">${cellContent}</span>`;
            } else if (col === 'duration') {
                cellContent = `<span class="duration-badge">${cellContent}</span>`;
            } else if (col === 'price') {
                cellContent = `<span class="price-highlight">${cellContent}</span>`;
            } else if (col === 'status') {
                cellContent = `<span class="status-badge ${statusClass}">${cellContent}</span>`;
            } else if (col === 'booking_id') {
                cellContent = `<span class="booking-id">${cellContent}</span>`;
            }

            html += `<td>${cellContent}</td>`;
        });

        html += `</tr>`;
    });

    html += '</tbody>';
    return html;
}

function generateGenericTable(cards) {
    let html = `
        <thead>
            <tr>
                <th>Name</th>
                <th>Details</th>
            </tr>
        </thead>
        <tbody>
    `;

    cards.forEach(card => {
        const id = card.getAttribute('data-id');
        const type = card.getAttribute('data-type') || 'item';
        const name = card.querySelector('.entity-title').textContent;
        const details = card.querySelector('.entity-subtitle') ? card.querySelector('.entity-subtitle').textContent : '-';

        html += `
            <tr class="table-row-clickable" data-id="${id}" data-type="${type}" onclick="viewEntity('${id}', '${type}')">
                <td>${name}</td>
                <td>${details}</td>
            </tr>
        `;
    });

    html += '</tbody>';
    return html;
}

// Initialize reordering functionality
function initializeReordering() {
    // Add sort functionality to existing tables
    addSortingToTables();

    // Listen for table view changes to add sorting to new tables
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && (node.classList.contains('entity-table') || node.querySelector('.table'))) {
                        addSortingToTables();
                    }
                });
            }
        });
    });

    observer.observe(document.body, { childList: true, subtree: true });
}

function addSortingToTables() {
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        const headers = table.querySelectorAll('th:not(:last-child)');
        headers.forEach((header, index) => {
            if (!header.querySelector('.sort-btn')) {
                const sortBtn = document.createElement('button');
                sortBtn.className = 'sort-btn';
                sortBtn.innerHTML = '<i class="fas fa-sort"></i>';
                sortBtn.onclick = (e) => {
                    e.preventDefault();
                    sortTable(header, table, index); // No offset needed since no checkbox column
                };
                header.appendChild(sortBtn);
                header.style.position = 'relative';
                header.style.cursor = 'pointer';
            }
        });
    });
}

// Sort table by column
function sortTable(header, table, columnIndex) {
    const tbody = table.querySelector('tbody');
    if (!tbody) return;

    const rows = Array.from(tbody.querySelectorAll('tr'));
    if (rows.length === 0) return;

    // Determine current sort direction
    const currentSort = header.getAttribute('data-sort') || 'none';
    const newSort = currentSort === 'asc' ? 'desc' : 'asc';

    // Clear all sort indicators in this table
    header.parentElement.querySelectorAll('th').forEach(th => {
        th.removeAttribute('data-sort');
        const sortBtn = th.querySelector('.sort-btn i');
        if (sortBtn) sortBtn.className = 'fas fa-sort';
    });

    // Set new sort indicator
    header.setAttribute('data-sort', newSort);
    const sortIcon = header.querySelector('.sort-btn i');
    if (sortIcon) {
        sortIcon.className = newSort === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    }

    // Sort rows
    rows.sort((a, b) => {
        const aCell = a.children[columnIndex];
        const bCell = b.children[columnIndex];

        if (!aCell || !bCell) return 0;

        // Get text content, handling nested elements
        let aValue = aCell.textContent.trim();
        let bValue = bCell.textContent.trim();

        // Handle special cases
        if (aValue === '-') aValue = '';
        if (bValue === '-') bValue = '';

        // Try to parse as currency first
        const aCurrency = aValue.match(/€?(\d+(?:\.\d+)?)/);
        const bCurrency = bValue.match(/€?(\d+(?:\.\d+)?)/);

        if (aCurrency && bCurrency) {
            const aNum = parseFloat(aCurrency[1]);
            const bNum = parseFloat(bCurrency[1]);
            return newSort === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // Try to parse as numbers
        const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return newSort === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // Handle dates
        const aDate = new Date(aValue);
        const bDate = new Date(bValue);

        if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
            return newSort === 'asc' ? aDate - bDate : bDate - aDate;
        }

        // Default to string comparison
        const comparison = aValue.localeCompare(bValue, undefined, { numeric: true, sensitivity: 'base' });
        return newSort === 'asc' ? comparison : -comparison;
    });

    // Add sorting animation
    tbody.style.transition = 'opacity 0.2s ease';
    tbody.style.opacity = '0.6';

    // Reorder rows in DOM
    setTimeout(() => {
        rows.forEach(row => tbody.appendChild(row));
        tbody.style.opacity = '1';

        // Show notification
        showNotification(`Sorted by ${header.textContent.trim()} (${newSort === 'asc' ? 'ascending' : 'descending'})`, 'success');
    }, 100);
}

// Initialize toolbar actions
function initializeToolbarActions() {
    // Export functionality
    const exportBtn = document.getElementById('export-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', handleExport);
    }

    // Import functionality
    const importBtn = document.getElementById('import-btn');
    if (importBtn) {
        importBtn.addEventListener('click', handleImport);
    }

    // Refresh functionality
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', handleRefresh);
    }

    // Settings functionality
    const settingsBtn = document.getElementById('settings-btn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', handleSettings);
    }
}

function getInfoValue(card, label) {
    const infoRows = card.querySelectorAll('.info-row');
    for (let row of infoRows) {
        const iconEl = row.querySelector('.info-icon i');
        if (iconEl) {
            // Map icon classes to labels
            let iconType = '';
            if (iconEl.classList.contains('fa-envelope')) iconType = 'Email';
            if (iconEl.classList.contains('fa-phone')) iconType = 'Phone';
            if (iconEl.classList.contains('fa-globe')) iconType = 'Language';
            if (iconEl.classList.contains('fa-info-circle')) iconType = 'Description';

            if (iconType === label) {
                const valueEl = row.querySelector('.info-value');
                return valueEl ? valueEl.textContent.trim() : '';
            }
        }
    }
    return '';
}

// Table checkboxes functionality removed - no longer needed

// Search functionality
function initializeSearch() {
    const searchInputs = document.querySelectorAll('.search-bar input, #search-input, #customer-search');

    searchInputs.forEach(input => {
        // Only search on Enter key press, not on typing
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch(this.value);
            }
        });
    });
}

function performSearch(query = '') {
    const searchInput = document.querySelector('.search-input');
    if (!searchInput) return;

    // If no query provided, get from input
    if (!query) {
        query = searchInput.value.trim();
    }

    // If still empty, clear results and return
    if (!query) {
        hideSearchResults();
        return;
    }

    // Show loading state
    showSearchLoading();

    // Perform global search via API
    // Use the API router (without .php extension)
    const apiUrl = `../api/search?q=${encodeURIComponent(query)}&limit=10`;

    console.log('Search API URL:', apiUrl); // Debug log

    fetch(apiUrl)
        .then(response => {
            console.log('Search response status:', response.status); // Debug log
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Search response data:', data); // Debug log
            if (data.success) {
                showSearchResults(data.data.results, query);
            } else {
                showSearchError(data.error || 'Search failed');
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            showSearchError(`Search failed: ${error.message}`);
        });
}

// Global Search Functions
function showSearchLoading() {
    const searchContainer = getOrCreateSearchContainer();
    searchContainer.innerHTML = `
        <div class="search-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Searching...</span>
        </div>
    `;
    searchContainer.style.display = 'block';
}

function showSearchResults(results, query) {
    const searchContainer = getOrCreateSearchContainer();

    if (results.length === 0) {
        searchContainer.innerHTML = `
            <div class="search-no-results">
                <i class="fas fa-search"></i>
                <span>No results found for "${query}"</span>
            </div>
        `;
    } else {
        const resultsHtml = results.map(result => `
            <div class="search-result-item" onclick="window.location.href='${result.url}'">
                <div class="search-result-icon">
                    <i class="${result.icon}"></i>
                </div>
                <div class="search-result-content">
                    <div class="search-result-title">${escapeHtml(result.title)}</div>
                    <div class="search-result-subtitle">${escapeHtml(result.subtitle)}</div>
                    <div class="search-result-description">${escapeHtml(result.description)}</div>
                </div>
                <div class="search-result-type">${result.type}</div>
            </div>
        `).join('');

        searchContainer.innerHTML = `
            <div class="search-results-header">
                <span>Search results for "${escapeHtml(query)}"</span>
                <button class="search-close" onclick="hideSearchResults()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results-list">
                ${resultsHtml}
            </div>
        `;
    }

    searchContainer.style.display = 'block';
}

function showSearchError(error) {
    const searchContainer = getOrCreateSearchContainer();
    searchContainer.innerHTML = `
        <div class="search-error">
            <i class="fas fa-exclamation-triangle"></i>
            <span>Error: ${escapeHtml(error)}</span>
        </div>
    `;
    searchContainer.style.display = 'block';
}

function hideSearchResults() {
    const searchContainer = document.getElementById('global-search-results');
    if (searchContainer) {
        searchContainer.style.display = 'none';
    }
}

function getOrCreateSearchContainer() {
    let container = document.getElementById('global-search-results');
    if (!container) {
        container = document.createElement('div');
        container.id = 'global-search-results';
        container.className = 'global-search-results';

        // Insert after header
        const header = document.querySelector('.header');
        if (header) {
            header.parentNode.insertBefore(container, header.nextSibling);
        } else {
            document.body.appendChild(container);
        }
    }
    return container;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Debounced search to avoid too many API calls
let searchTimeout;
function debounceGlobalSearch(query) {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        performSearch(query);
    }, 300); // Wait 300ms after user stops typing
}

// Handle keyboard events in search
function handleSearchKeydown(event) {
    if (event.key === 'Escape') {
        hideSearchResults();
        event.target.blur();
    } else if (event.key === 'Enter') {
        event.preventDefault();
        performSearch(event.target.value);
    }
}

// Close search results when clicking outside
document.addEventListener('click', function(event) {
    const searchContainer = document.getElementById('global-search-results');
    const searchInput = document.querySelector('.search-input');

    if (searchContainer &&
        !searchContainer.contains(event.target) &&
        !searchInput.contains(event.target)) {
        hideSearchResults();
    }
});

// Filter functionality
function initializeFilters() {
    const filterSelects = document.querySelectorAll('.filter-group select:not(.custom-filter), .toolbar select:not(.custom-filter)');

    // Restore filter values from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    filterSelects.forEach(select => {
        const paramName = select.id.replace('-filter', '');
        const paramValue = urlParams.get(paramName);
        if (paramValue) {
            select.value = paramValue;
        }
    });

    // Add change event listeners
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            applyFilters();
        });
    });

    // Apply filters on page load if any are set
    const hasFilters = Array.from(filterSelects).some(select => select.value);
    if (hasFilters) {
        setTimeout(() => applyFilters(), 100);
    }
}

function applyFiltersWithURL() {
    const filters = document.querySelectorAll('select[id$="-filter"]');
    const urlParams = new URLSearchParams(window.location.search);

    // Update URL parameters with current filter values
    filters.forEach(filter => {
        const paramName = filter.id.replace('-filter', '');
        if (filter.value) {
            urlParams.set(paramName, filter.value);
        } else {
            urlParams.delete(paramName);
        }
    });

    // Reset to first page when filters change
    urlParams.set('page_num', '1');

    // Redirect with new parameters
    window.location.href = window.location.pathname + '?' + urlParams.toString();
}

// Filter helper functions
function checkStatusFilter(card, filterValue) {
    // Check status badge first
    const statusBadge = card.querySelector('.status-badge');
    if (statusBadge) {
        const badgeText = statusBadge.textContent.trim().toLowerCase();
        return badgeText === filterValue.toLowerCase();
    }

    // Check entity card classes
    if (filterValue.toLowerCase() === 'active') {
        return card.classList.contains('entity-card--active') ||
               !card.classList.contains('entity-card--inactive');
    } else if (filterValue.toLowerCase() === 'inactive') {
        return card.classList.contains('entity-card--inactive');
    }

    return true;
}

function checkCategoryFilter(card, filterValue) {
    const categoryBadge = card.querySelector('.category-badge');
    if (categoryBadge) {
        const categoryText = categoryBadge.textContent.trim().toLowerCase();
        return categoryText.includes(filterValue.toLowerCase());
    }
    return false;
}

function checkEmployeeFilter(card, filterValue) {
    if (filterValue === 'all') return true;

    // Check for employee name in various places
    const employeeElements = card.querySelectorAll('.employee-name, .info-value, .entity-title');
    for (let element of employeeElements) {
        if (element.textContent.toLowerCase().includes(filterValue.toLowerCase())) {
            return true;
        }
    }
    return false;
}

function checkVisitFilter(card, filterValue) {
    // Find the last visit stat
    const statItems = card.querySelectorAll('.stat-item');
    for (let statItem of statItems) {
        const label = statItem.querySelector('.stat-label');
        if (label && label.textContent.toLowerCase().includes('visit')) {
            const value = statItem.querySelector('.stat-value');
            if (value) {
                const visitText = value.textContent.trim().toLowerCase();

                // Simple date filtering logic
                const now = new Date();
                const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                const oneQuarterAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

                if (visitText === 'never') {
                    return false;
                }

                // This is a simplified check - in a real app you'd parse the actual date
                switch (filterValue) {
                    case 'week':
                        return !visitText.includes('month') && !visitText.includes('year');
                    case 'month':
                        return visitText.includes('day') || visitText.includes('week');
                    case 'quarter':
                        return !visitText.includes('year');
                    default:
                        return true;
                }
            }
        }
    }
    return true;
}

function checkDateFilter(card, filterValue) {
    // For reservations, check the date
    const dateElements = card.querySelectorAll('.date-value, .reservation-datetime, .entity-subtitle');
    for (let element of dateElements) {
        const dateText = element.textContent.trim();
        if (dateText.includes(filterValue)) {
            return true;
        }
    }
    return false;
}

function applyFilters() {
    const filters = document.querySelectorAll('select[id$="-filter"]');
    const cards = document.querySelectorAll('.entity-card:not(.add-card)');
    const tableRows = document.querySelectorAll('.table tbody tr');

    // Get current filter values
    const filterValues = {};
    filters.forEach(filter => {
        const paramName = filter.id.replace('-filter', '');
        filterValues[paramName] = filter.value;
    });

    let visibleCount = 0;

    // Filter cards
    cards.forEach(card => {
        let shouldShow = true;

        // Apply each filter
        Object.keys(filterValues).forEach(filterType => {
            const filterValue = filterValues[filterType];
            if (!filterValue) return; // Skip empty filters

            switch(filterType) {
                case 'status':
                    shouldShow = shouldShow && checkStatusFilter(card, filterValue);
                    break;

                case 'category':
                    shouldShow = shouldShow && checkCategoryFilter(card, filterValue);
                    break;

                case 'employee':
                    shouldShow = shouldShow && checkEmployeeFilter(card, filterValue);
                    break;

                case 'visit':
                    shouldShow = shouldShow && checkVisitFilter(card, filterValue);
                    break;

                case 'date':
                    shouldShow = shouldShow && checkDateFilter(card, filterValue);
                    break;
            }
        });

        // Show/hide card
        if (shouldShow) {
            card.style.display = '';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    // Filter table rows if in table view
    tableRows.forEach(row => {
        let shouldShow = true;

        Object.keys(filterValues).forEach(filterType => {
            const filterValue = filterValues[filterType];
            if (!filterValue) return;

            // Get row data based on column positions
            const cells = row.querySelectorAll('td');
            if (cells.length === 0) return;

            switch(filterType) {
                case 'status':
                    const statusCell = row.querySelector('.status-badge');
                    if (statusCell && !statusCell.textContent.toLowerCase().includes(filterValue.toLowerCase())) {
                        shouldShow = false;
                    }
                    break;

                case 'category':
                    const categoryCell = row.querySelector('.category-badge');
                    if (categoryCell && !categoryCell.textContent.toLowerCase().includes(filterValue.toLowerCase())) {
                        shouldShow = false;
                    }
                    break;
            }
        });

        row.style.display = shouldShow ? '' : 'none';
    });

    // Update results count
    updateResultsCount(visibleCount);

    // Show notification
    showNotification(`Showing ${visibleCount} results`, 'info');
}

function updateResultsCount(count) {
    const resultCountElement = document.querySelector('.results-count');
    if (resultCountElement) {
        resultCountElement.textContent = `${count} results`;
    }
}

// Sidebar functionality
function initializeSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    // Handle sidebar toggle button (inside sidebar)
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
                overlay.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
            }
        });
    }

    // Handle mobile menu button (in header)
    if (mobileMenuBtn && sidebar) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        });
    }

    // Handle overlay click
    if (overlay) {
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('show', 'collapsed');
            this.classList.remove('show');
        });
    }

    // Enhanced responsive behavior
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            }

            // Auto-adjust view based on screen size if no user preference
            const savedPreference = localStorage.getItem('admin-view-preference');
            if (!savedPreference) {
                const newDefaultView = getResponsiveDefaultView();
                const currentActiveButton = document.querySelector('.view-toggle button.active');
                const currentView = currentActiveButton ? currentActiveButton.getAttribute('data-view') : null;

                if (currentView !== newDefaultView) {
                    setActiveView(newDefaultView);
                }
            }
        }, 250); // Debounce resize events
    });
}

// Tooltips functionality
function initializeTooltips() {
    let tooltip = document.getElementById('admin-tooltip');
    if (!tooltip) {
        tooltip = document.createElement('div');
        tooltip.id = 'admin-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            display: none;
            pointer-events: none;
        `;
        document.body.appendChild(tooltip);
    }

    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            showTooltip(this, this.getAttribute('data-tooltip'));
        });

        element.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });
}

function showTooltip(element, text) {
    const tooltip = document.getElementById('admin-tooltip');
    if (!tooltip) return;

    tooltip.textContent = text;
    tooltip.style.display = 'block';

    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();

    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
    let top = rect.top - tooltipRect.height - 8;

    // Adjust if tooltip goes off screen
    if (left < 0) left = 8;
    if (left + tooltipRect.width > window.innerWidth) {
        left = window.innerWidth - tooltipRect.width - 8;
    }
    if (top < 0) {
        top = rect.bottom + 8;
    }

    tooltip.style.left = left + 'px';
    tooltip.style.top = top + 'px';
}

function hideTooltip() {
    const tooltip = document.getElementById('admin-tooltip');
    if (tooltip) {
        tooltip.style.display = 'none';
    }
}



// Form validation
function initializeFormValidation() {
    document.addEventListener('submit', function(e) {
        const form = e.target;
        if (form.classList.contains('modal-form') || form.classList.contains('entity-form')) {
            if (!validateForm(form)) {
                e.preventDefault();
                return false;
            }
        }
    });
}

function validateForm(form) {
    if (!form) return false;

    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    let firstErrorField = null;

    requiredFields.forEach(field => {
        clearFieldError(field);

        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
            if (!firstErrorField) {
                firstErrorField = field;
            }
        } else if (field.type === 'email' && !isValidEmail(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            isValid = false;
            if (!firstErrorField) {
                firstErrorField = field;
            }
        } else if (field.type === 'tel' && field.value && !isValidPhone(field.value)) {
            showFieldError(field, 'Please enter a valid phone number');
            isValid = false;
            if (!firstErrorField) {
                firstErrorField = field;
            }
        }
    });

    if (!isValid && firstErrorField) {
        firstErrorField.focus();
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    return isValid;
}

function showFieldError(field, message) {
    clearFieldError(field);

    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.cssText = 'color: #ef4444; font-size: 12px; margin-top: 4px;';

    field.style.borderColor = '#ef4444';
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    field.style.borderColor = '';
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^(69|21|22|23|24|25|26|27|28)\d{8}$/;
    const cleanPhone = phone.replace(/\D/g, '');
    return phoneRegex.test(cleanPhone);
}





function handleFormSubmit(form, onSubmit) {
    if (!form) return;

    // Validate form
    if (!validateForm(form)) {
        return;
    }

    // Get form data
    const formData = new FormData(form);

    // Call submit handler
    if (onSubmit) {
        onSubmit(formData);
    }
}

// AJAX Helper
function makeAjaxCall(url, formData, successCallback, errorCallback) {
    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(text => {
        try {
            const data = JSON.parse(text);
            if (successCallback) {
                successCallback(data);
            }
        } catch (parseError) {
            if (errorCallback) {
                errorCallback('Invalid response from server');
            } else {
                showNotification('Invalid response from server', 'error');
            }
        }
    })
    .catch(error => {
        if (errorCallback) {
            errorCallback('Network error occurred');
        } else {
            showNotification('Network error occurred', 'error');
        }
    });
}

// Notification System
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#06b6d4'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10001;
        max-width: 400px;
        font-size: 14px;
        line-height: 1.4;
    `;

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);

    // Click to dismiss
    notification.onclick = function() {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}

// CRUD Functions - Customer
function addCustomer() {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=add-customer';
}

function editCustomer(id) {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=edit-customer&id=' + id;
}

function viewCustomer(id) {
    window.location.href = '/store-admin/?page=view-customer&id=' + id;
}

// CRUD Functions - Service
function addService() {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=add-service';
}

function editService(id) {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=edit-service&id=' + id;
}

function viewService(id) {
    showLoadingOverlay('Loading details...');
    window.location.href = '/store-admin/?page=view-service&id=' + id;
}

// CRUD Functions - Employee
function addEmployee() {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=add-employee';
}

function editEmployee(id) {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=edit-employee&id=' + id;
}

function viewEmployee(id) {
    showLoadingOverlay('Loading details...');
    window.location.href = '/store-admin/?page=view-employee&id=' + id;
}

// CRUD Functions - Category
function addCategory() {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=add-category';
}

function editCategory(id) {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=edit-category&id=' + id;
}

function viewCategory(id) {
    window.location.href = '/store-admin/?page=view-category&id=' + id;
}

// Generic view function for entity cards
function viewEntity(id, type) {
    switch(type) {
        case 'customer':
            viewCustomer(id);
            break;
        case 'service':
            viewService(id);
            break;
        case 'employee':
            viewEmployee(id);
            break;
        case 'category':
            viewCategory(id);
            break;
        case 'reservation':
            viewReservation(id);
            break;
        default:
            console.warn('Unknown entity type:', type);
    }
}

// CRUD Functions - Reservation
function addReservation() {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=add-reservation';
}

function editReservation(id) {
    showLoadingOverlay('Loading form...');
    window.location.href = '/store-admin/?page=edit-reservation&id=' + id;
}

function viewReservation(id) {
    window.location.href = '/store-admin/?page=view-reservation&id=' + id;
}

function confirmReservation(id) {
    if (confirm('Are you sure you want to confirm this reservation?')) {
        updateReservationStatus(id, 'confirmed', 'Reservation confirmed successfully');
    }
}

function completeReservation(id) {
    if (confirm('Are you sure you want to mark this reservation as completed?')) {
        updateReservationStatus(id, 'completed', 'Reservation completed successfully');
    }
}

function cancelReservation(id) {
    if (confirm('Are you sure you want to cancel this reservation?')) {
        updateReservationStatus(id, 'cancelled', 'Reservation cancelled successfully');
    }
}

function updateReservationStatus(id, status, successMessage) {
    const formData = new FormData();
    formData.append('action', 'update_reservation_status');
    formData.append('id', id);
    formData.append('status', status);

    makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
        if (data.success) {
            showNotification(successMessage, 'success');
            // Reload the page to reflect changes
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification('Error updating reservation: ' + (data.error || 'Unknown error'), 'error');
        }
    });
}

// Calendar and bulk action functions for reservations
function showCalendar() {
    window.location.href = '/store-admin/?page=calendar';
}

function bulkConfirm() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('Please select reservations to confirm', 'warning');
        return;
    }

    if (confirm(`Are you sure you want to confirm ${selectedIds.length} reservation(s)?`)) {
        bulkUpdateStatus(selectedIds, 'confirmed', 'Reservations confirmed successfully');
    }
}

function bulkRemind() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('Please select reservations to send reminders', 'warning');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk remind feature temporarily disabled during modal removal', 'warning');
}

function bulkReschedule() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('Please select reservations to reschedule', 'warning');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk reschedule feature temporarily disabled during modal removal', 'warning');
}

function bulkCancel() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('Please select reservations to cancel', 'warning');
        return;
    }

    if (confirm(`Are you sure you want to cancel ${selectedIds.length} reservation(s)?`)) {
        bulkUpdateStatus(selectedIds, 'cancelled', 'Reservations cancelled successfully');
    }
}

function bulkUpdateStatus(ids, status, successMessage) {
    const formData = new FormData();
    formData.append('action', 'bulk_update_reservation_status');
    formData.append('ids', JSON.stringify(ids));
    formData.append('status', status);

    makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
        if (data.success) {
            showNotification(successMessage, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification('Error updating reservations: ' + (data.error || 'Unknown error'), 'error');
        }
    });
}

function getSelectedIds() {
    const checkboxes = document.querySelectorAll('.entity-select:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Form Submission
function submitForm(entity, action, formData, id = null) {
    // Show loading state
    showNotification('Saving...', 'info');

    // Prepare the action for the AJAX controller
    // Handle proper singular forms
    let entitySingular;
    switch (entity) {
        case 'categories':
            entitySingular = 'category';
            break;
        case 'services':
            entitySingular = 'service';
            break;
        case 'customers':
            entitySingular = 'customer';
            break;
        case 'employees':
            entitySingular = 'employee';
            break;
        case 'reservations':
            entitySingular = 'reservation';
            break;
        default:
            entitySingular = entity.endsWith('s') ? entity.slice(0, -1) : entity;
    }
    const actionName = `save_${entitySingular}`;

    // Remove existing action field and add the correct one
    formData.delete('action');
    formData.append('action', actionName);

    if (id) {
        formData.set('id', id); // Use set instead of append to avoid duplicates
    }

    fetch('/store-admin/controllers/ajax.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(text => {
        try {
            const data = JSON.parse(text);
            if (data.success) {
                showNotification(`${entitySingular} ${action === 'add' ? 'created' : 'updated'} successfully`, 'success');
                // Reload page to show changes
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(data.message || data.error || `Failed to ${action} ${entitySingular}`, 'error');
            }
        } catch (parseError) {
            showNotification('Invalid response from server', 'error');
        }
    })
    .catch(error => {
        showNotification('Network error occurred', 'error');
    });
}

// Translation System Functions
function showLoading(message = 'Loading...') {
    // Remove existing loading
    const existingLoading = document.querySelector('.loading-overlay');
    if (existingLoading) {
        existingLoading.remove();
    }

    // Create loading overlay
    const loading = document.createElement('div');
    loading.className = 'loading-overlay';
    loading.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10002;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
    `;

    loading.innerHTML = `
        <div style="text-align: center;">
            <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px;"></i>
            <div>${message}</div>
        </div>
    `;

    document.body.appendChild(loading);
}

function hideLoading() {
    const loading = document.querySelector('.loading-overlay');
    if (loading) {
        loading.remove();
    }
}

function showSuccess(message) {
    showNotification(message, 'success');
}

function showError(message) {
    showNotification(message, 'error');
}

// View Generation Functions (simplified versions)
function generateCustomerView(customer) {
    const totalVisits = customer.total_visits || 0;
    const totalSpent = customer.total_spent || 0;
    const lastVisit = customer.last_visit || null;
    const customerType = totalVisits > 10 ? 'VIP' : (totalVisits > 3 ? 'Regular' : 'New');

    return `
        <div class="entity-view customer-view">
            <!-- Customer Header -->
            <div class="view-header">
                <div class="view-title-section">
                    <h3 class="view-title">${customer.name}</h3>
                    <div class="view-badges">
                        <span class="status-badge status-${customer.status === 'active' ? 'active' : 'inactive'}">
                            ${customer.status === 'active' ? 'Active' : 'Inactive'}
                        </span>
                        <span class="customer-type-badge ${customerType.toLowerCase()}">${customerType} Customer</span>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-address-card"></i> Contact Information
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Email</span>
                            ${customer.email ?
                                `<a href="mailto:${customer.email}" class="info-value contact-link">${customer.email}</a>` :
                                '<span class="info-value text-muted">Not provided</span>'
                            }
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Phone</span>
                            ${customer.phone ?
                                `<a href="tel:${customer.phone}" class="info-value contact-link">${customer.phone}</a>` :
                                '<span class="info-value text-muted">Not provided</span>'
                            }
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Language</span>
                            <span class="info-value">
                                <span class="language-badge">${(customer.language || 'EL').toUpperCase()}</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Statistics -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-chart-bar"></i> Customer Statistics
                </h4>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">${totalVisits}</div>
                        <div class="stat-label">Total Visits</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">€${totalSpent.toFixed(2)}</div>
                        <div class="stat-label">Total Spent</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${lastVisit ? formatDate(lastVisit) : 'Never'}</div>
                        <div class="stat-label">Last Visit</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">€${totalVisits > 0 ? (totalSpent / totalVisits).toFixed(2) : '0.00'}</div>
                        <div class="stat-label">Avg. per Visit</div>
                    </div>
                </div>
            </div>

            ${customer.notes ? `
            <!-- Notes -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-sticky-note"></i> Notes
                </h4>
                <div class="notes-content">
                    <p>${customer.notes}</p>
                </div>
            </div>
            ` : ''}

            <!-- Account Information -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-info-circle"></i> Account Information
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Member Since</span>
                            <span class="info-value">${formatDate(customer.created_at)}</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Last Updated</span>
                            <span class="info-value">${formatDate(customer.updated_at || customer.created_at)}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateServiceView(service) {
    const totalDuration = (service.preparation_time || 0) + service.duration + (service.cleanup_time || 0);

    return `
        <div class="entity-view service-view">
            <!-- Service Header -->
            <div class="view-header">
                <div class="view-title-section">
                    <h3 class="view-title">${service.name}</h3>
                    <div class="view-badges">
                        <span class="status-badge status-${service.is_active ? 'active' : 'inactive'}">
                            ${service.is_active ? 'Active' : 'Inactive'}
                        </span>
                        ${service.category_name ?
                            `<span class="category-badge" ${service.category_color ? `style="background-color: ${service.category_color}; color: white;"` : ''}>
                                ${service.category_name}
                            </span>` : ''
                        }
                    </div>
                </div>
            </div>

            <!-- Service Details -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-info-circle"></i> Service Details
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Duration</span>
                            <span class="info-value">
                                <span class="duration-badge">${service.duration} min</span>
                            </span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-euro-sign"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Price</span>
                            <span class="info-value">
                                <span class="price-badge">€${parseFloat(service.price).toFixed(2)}</span>
                            </span>
                        </div>
                    </div>

                    ${totalDuration !== service.duration ? `
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-hourglass-half"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Total Time</span>
                            <span class="info-value">
                                <span class="duration-badge">${totalDuration} min</span>
                            </span>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>

            ${service.description ? `
            <!-- Description -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-align-left"></i> Description
                </h4>
                <div class="description-content">
                    <p>${service.description}</p>
                </div>
            </div>
            ` : ''}

            ${(service.preparation_time > 0 || service.cleanup_time > 0) ? `
            <!-- Time Breakdown -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-tasks"></i> Time Breakdown
                </h4>
                <div class="timing-breakdown-view">
                    ${service.preparation_time > 0 ? `
                    <div class="timing-item prep">
                        <i class="fas fa-clock"></i>
                        <span>Preparation: ${service.preparation_time} min</span>
                    </div>
                    ` : ''}

                    <div class="timing-item main">
                        <i class="fas fa-cut"></i>
                        <span>Service: ${service.duration} min</span>
                    </div>

                    ${service.cleanup_time > 0 ? `
                    <div class="timing-item cleanup">
                        <i class="fas fa-broom"></i>
                        <span>Cleanup: ${service.cleanup_time} min</span>
                    </div>
                    ` : ''}
                </div>
            </div>
            ` : ''}

            <!-- Service Information -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-cog"></i> Service Information
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Created</span>
                            <span class="info-value">${formatDate(service.created_at)}</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Last Updated</span>
                            <span class="info-value">${formatDate(service.updated_at || service.created_at)}</span>
                        </div>
                    </div>

                    ${service.employee_selection ? `
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Employee Assignment</span>
                            <span class="info-value">${service.employee_selection === 'auto' ? 'Auto-assign' : 'Manual selection'}</span>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

function generateEmployeeView(employee) {
    const totalBookings = employee.total_bookings || 0;
    const totalRevenue = employee.total_revenue || 0;
    const avgRating = employee.avg_rating || 0;
    const services = employee.services || [];
    const workingHours = employee.working_hours ? JSON.parse(employee.working_hours) : {};

    return `
        <div class="entity-view employee-view">
            <!-- Employee Header -->
            <div class="view-header">
                <div class="view-title-section">
                    <div class="employee-avatar-large" style="background-color: ${employee.color || '#3498db'}">
                        ${employee.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                        <h3 class="view-title">${employee.name}</h3>
                        <div class="view-badges">
                            <span class="status-badge status-${employee.is_active ? 'active' : 'inactive'}">
                                ${employee.is_active ? 'Active' : 'Inactive'}
                            </span>
                            ${employee.position ? `<span class="position-badge">${employee.position}</span>` : ''}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-address-card"></i> Contact Information
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Email</span>
                            ${employee.email ?
                                `<a href="mailto:${employee.email}" class="info-value contact-link">${employee.email}</a>` :
                                '<span class="info-value text-muted">Not provided</span>'
                            }
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Phone</span>
                            ${employee.phone ?
                                `<a href="tel:${employee.phone}" class="info-value contact-link">${employee.phone}</a>` :
                                '<span class="info-value text-muted">Not provided</span>'
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employee Statistics -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-chart-bar"></i> Performance Statistics
                </h4>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">${totalBookings}</div>
                        <div class="stat-label">Total Bookings</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">€${totalRevenue.toFixed(2)}</div>
                        <div class="stat-label">Total Revenue</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${avgRating.toFixed(1)}/5</div>
                        <div class="stat-label">Avg. Rating</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${services.length}</div>
                        <div class="stat-label">Services</div>
                    </div>
                </div>
            </div>

            ${services.length > 0 ? `
            <!-- Services -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-cut"></i> Services Offered
                </h4>
                <div class="services-list">
                    ${services.map(service => `
                        <div class="service-item">
                            <span class="service-name">${service.name}</span>
                            <span class="service-price">€${parseFloat(service.price).toFixed(2)}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            ${Object.keys(workingHours).length > 0 ? `
            <!-- Working Hours -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-clock"></i> Working Hours
                </h4>
                <div class="working-hours-list">
                    ${Object.entries(workingHours).map(([day, hours]) => `
                        <div class="working-day">
                            <span class="day-name">${day.charAt(0).toUpperCase() + day.slice(1)}</span>
                            <span class="day-hours">
                                ${Array.isArray(hours) && hours.length > 0 ?
                                    hours.map(h => `${h.start} - ${h.end}`).join(', ') :
                                    'Closed'
                                }
                            </span>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            <!-- Account Information -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-info-circle"></i> Account Information
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Hired Date</span>
                            <span class="info-value">${formatDate(employee.created_at)}</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Last Updated</span>
                            <span class="info-value">${formatDate(employee.updated_at || employee.created_at)}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateCategoryView(category) {
    const services = category.services || [];
    const totalRevenue = category.total_revenue || 0;
    const totalBookings = category.total_bookings || 0;

    return `
        <div class="entity-view category-view">
            <!-- Category Header -->
            <div class="view-header">
                <div class="view-title-section">
                    <div class="category-icon-large" style="background-color: ${category.color || '#3498db'}">
                        ${category.icon ? `<i class="${category.icon}"></i>` : category.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                        <h3 class="view-title">${category.name}</h3>
                        <div class="view-badges">
                            <span class="status-badge status-${category.is_active ? 'active' : 'inactive'}">
                                ${category.is_active ? 'Active' : 'Inactive'}
                            </span>
                            <span class="sort-badge">Order: ${category.sort_order || 0}</span>
                        </div>
                    </div>
                </div>
            </div>

            ${category.description ? `
            <!-- Description -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-align-left"></i> Description
                </h4>
                <div class="description-content">
                    <p>${category.description}</p>
                </div>
            </div>
            ` : ''}

            <!-- Category Statistics -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-chart-bar"></i> Category Statistics
                </h4>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">${services.length}</div>
                        <div class="stat-label">Total Services</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${totalBookings}</div>
                        <div class="stat-label">Total Bookings</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">€${totalRevenue.toFixed(2)}</div>
                        <div class="stat-label">Total Revenue</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">€${services.length > 0 ? (totalRevenue / services.length).toFixed(2) : '0.00'}</div>
                        <div class="stat-label">Avg. per Service</div>
                    </div>
                </div>
            </div>

            ${services.length > 0 ? `
            <!-- Services in Category -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-list"></i> Services in this Category
                </h4>
                <div class="category-services-list">
                    ${services.map(service => `
                        <div class="category-service-item">
                            <div class="service-info">
                                <span class="service-name">${service.name}</span>
                                <span class="service-duration">${service.duration} min</span>
                            </div>
                            <div class="service-pricing">
                                <span class="service-price">€${parseFloat(service.price).toFixed(2)}</span>
                                <span class="service-status status-${service.is_active ? 'active' : 'inactive'}">
                                    ${service.is_active ? 'Active' : 'Inactive'}
                                </span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            <!-- Category Settings -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-cog"></i> Category Settings
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Color</span>
                            <span class="info-value">
                                <span class="color-swatch" style="background-color: ${category.color || '#3498db'}"></span>
                                ${category.color || '#3498db'}
                            </span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-sort-numeric-up"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Sort Order</span>
                            <span class="info-value">${category.sort_order || 0}</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Created</span>
                            <span class="info-value">${formatDate(category.created_at)}</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Last Updated</span>
                            <span class="info-value">${formatDate(category.updated_at || category.created_at)}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateReservationView(reservation) {
    const reservationDate = new Date(reservation.date + ' ' + reservation.start_time);
    const endTime = reservation.end_time || 'Not set';
    const duration = reservation.service_duration || 0;
    const price = reservation.service_price || 0;

    return `
        <div class="entity-view reservation-view">
            <!-- Reservation Header -->
            <div class="view-header">
                <div class="view-title-section">
                    <h3 class="view-title">Reservation #${reservation.id}</h3>
                    <div class="view-badges">
                        <span class="status-badge status-${reservation.status}">
                            ${reservation.status.charAt(0).toUpperCase() + reservation.status.slice(1)}
                        </span>
                        <span class="date-badge">
                            ${formatDate(reservation.date)}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Appointment Details -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-calendar-alt"></i> Appointment Details
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Date</span>
                            <span class="info-value">${formatDate(reservation.date)}</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Time</span>
                            <span class="info-value">${reservation.start_time}${endTime !== 'Not set' ? ' - ' + endTime : ''}</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-hourglass-half"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Duration</span>
                            <span class="info-value">${duration} minutes</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-euro-sign"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Price</span>
                            <span class="info-value">€${parseFloat(price).toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-user"></i> Customer Information
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Name</span>
                            <span class="info-value">${reservation.customer_name || 'Unknown'}</span>
                        </div>
                    </div>

                    ${reservation.customer_email ? `
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Email</span>
                            <a href="mailto:${reservation.customer_email}" class="info-value contact-link">${reservation.customer_email}</a>
                        </div>
                    </div>
                    ` : ''}

                    ${reservation.customer_phone ? `
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Phone</span>
                            <a href="tel:${reservation.customer_phone}" class="info-value contact-link">${reservation.customer_phone}</a>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>

            <!-- Service & Staff -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-cut"></i> Service & Staff
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-concierge-bell"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Service</span>
                            <span class="info-value">${reservation.service_name || 'Unknown'}</span>
                        </div>
                    </div>

                    ${reservation.employee_name ? `
                    <div class="info-item">
                        <div class="info-icon">
                            <div class="employee-avatar-small" style="background-color: ${reservation.employee_color || '#3498db'}">
                                ${reservation.employee_name.charAt(0).toUpperCase()}
                            </div>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Employee</span>
                            <span class="info-value">${reservation.employee_name}</span>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>

            ${reservation.notes ? `
            <!-- Notes -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-sticky-note"></i> Notes
                </h4>
                <div class="notes-content">
                    <p>${reservation.notes}</p>
                </div>
            </div>
            ` : ''}

            <!-- Reservation Information -->
            <div class="view-section">
                <h4 class="section-title">
                    <i class="fas fa-info-circle"></i> Reservation Information
                </h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Created</span>
                            <span class="info-value">${formatDate(reservation.created_at)}</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Last Updated</span>
                            <span class="info-value">${formatDate(reservation.updated_at || reservation.created_at)}</span>
                        </div>
                    </div>

                    ${reservation.confirmed_at ? `
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="info-content">
                            <span class="info-label">Confirmed</span>
                            <span class="info-value">${formatDate(reservation.confirmed_at)}</span>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

// Utility Functions
function formatDate(dateString) {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Additional helper functions for booking appointments
function bookAppointment(customerId) {
    window.location.href = `/store-admin/?page=add-reservation&customer_id=${customerId}`;
}

function manageCategories() {
    window.location.href = '/store-admin/?page=categories';
}

function manageSchedules() {
    // Redirect to business hours settings since schedules are managed there
    window.location.href = '/store-admin/?page=settings&tab=business_hours';
}

function manageSchedule(employeeId) {
    // For now, redirect to employee edit page where working hours can be managed
    editEmployee(employeeId);
}

function reorderCategories() {
    // Show loading overlay
    showLoadingOverlay('Loading reorder interface...');

    // Redirect to reorder page
    window.location.href = '/store-admin/?page=reorder-categories';
}

function showCalendar() {
    window.location.href = '/store-admin/?page=calendar';
}

// Service-specific functions
function duplicateService(id) {
    const formData = new FormData();
    formData.append('action', 'duplicate_service');
    formData.append('id', id);

    makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
        if (data.success) {
            showNotification('Service duplicated successfully', 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Error duplicating service: ' + (data.error || 'Unknown error'), 'error');
        }
    });
}

function bulkUpdatePrices() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select services first', 'warning');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk price update feature temporarily disabled during modal removal', 'warning');
}

function bulkDuplicate() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    // Determine the current page/table type
    const currentPage = getCurrentPageType();
    if (!currentPage) {
        showNotification('Cannot determine item type for duplication', 'error');
        return;
    }

    if (!['services', 'categories'].includes(currentPage)) {
        showNotification('Duplication is only supported for services and categories', 'warning');
        return;
    }

    if (confirm(`Are you sure you want to duplicate ${selected.length} ${currentPage}(s)?`)) {
        const formData = new FormData();
        formData.append('action', 'bulk_duplicate');
        formData.append('ids', JSON.stringify(selected));
        formData.append('table', currentPage);

        makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('Error duplicating items: ' + (data.error || 'Unknown error'), 'error');
            }
        });
    }
}

function bulkToggleStatus() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    // Determine the current page/table type
    const currentPage = getCurrentPageType();
    if (!currentPage) {
        showNotification('Cannot determine item type for status toggle', 'error');
        return;
    }

    if (!['services', 'employees'].includes(currentPage)) {
        showNotification('Status toggle is only supported for services and employees', 'warning');
        return;
    }

    if (confirm(`Are you sure you want to toggle status for ${selected.length} ${currentPage}(s)?`)) {
        const formData = new FormData();
        formData.append('action', 'bulk_toggle_status');
        formData.append('ids', JSON.stringify(selected));
        formData.append('table', currentPage);

        makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('Error toggling status: ' + (data.error || 'Unknown error'), 'error');
            }
        });
    }
}

// Bulk action functions
function bulkDelete() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    // Determine the current page/table type
    const currentPage = getCurrentPageType();
    if (!currentPage) {
        showNotification('Cannot determine item type for deletion', 'error');
        return;
    }

    if (confirm(`Are you sure you want to delete ${selected.length} ${currentPage}(s)? This action cannot be undone.`)) {
        const formData = new FormData();
        formData.append('action', 'bulk_delete');
        formData.append('ids', JSON.stringify(selected));
        formData.append('table', currentPage);

        makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('Error deleting items: ' + (data.error || 'Unknown error'), 'error');
            }
        });
    }
}

function bulkEmail() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk email feature temporarily disabled during modal removal', 'warning');
}

function bulkSMS() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select customers first', 'warning');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk SMS feature temporarily disabled during modal removal', 'warning');
}

function bulkExport() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select items first', 'warning');
        return;
    }

    // Determine the current page/table type
    const currentPage = getCurrentPageType();
    if (!currentPage) {
        showNotification('Cannot determine item type for export', 'error');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk export feature temporarily disabled during modal removal', 'warning');
}

// getSelectedItems function removed - no longer needed without checkboxes

function getCurrentPageType() {
    // Determine current page type from URL or page content
    const urlParams = new URLSearchParams(window.location.search);
    const page = urlParams.get('page');

    // Map page names to table names
    const pageMapping = {
        'customers': 'customers',
        'services': 'services',
        'categories': 'categories',
        'employees': 'employees',
        'reservations': 'reservations'
    };

    return pageMapping[page] || null;
}

// Category-specific bulk functions
function bulkReorder() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select categories first', 'warning');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk reorder feature temporarily disabled during modal removal', 'warning');
}

function bulkUpdateColors() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select categories first', 'warning');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk update colors feature temporarily disabled during modal removal', 'warning');
}

// Employee-specific bulk functions
function bulkSchedule() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select employees first', 'warning');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk schedule feature temporarily disabled during modal removal', 'warning');
}

function bulkNotify() {
    const selected = getSelectedItems();
    if (selected.length === 0) {
        showNotification('Please select employees first', 'warning');
        return;
    }

    // TODO: Convert to dedicated page
    showNotification('Bulk notify feature temporarily disabled during modal removal', 'warning');
}

// Additional Action Functions
function sendMessage(id) {
    showNotification('Message feature coming soon', 'info');
}

function sendEmail(id) {
    showNotification('Email feature coming soon', 'info');
}

function sendSMS(id) {
    showNotification('SMS feature coming soon', 'info');
}

function sendReminder(id) {
    showNotification('Reminder feature coming soon', 'info');
}

function sendNotification(id) {
    showNotification('Notification feature coming soon', 'info');
}

function rescheduleReservation(id) {
    showNotification('Reschedule feature coming soon', 'info');
}

function duplicateReservation(id) {
    showNotification('Duplicate reservation feature coming soon', 'info');
}

function confirmReservation(id) {
    if (confirm('Are you sure you want to confirm this reservation? This will send a confirmation email to the customer.')) {
        fetch('/api/admin-confirm-reservation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reservation_id: id,
                action: 'confirm'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const message = data.data?.message || data.message || 'Reservation confirmed successfully. Confirmation email sent to customer.';
                showNotification(message, 'success');
                // Reload the page to update the status
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                const errorMessage = data.error || data.message || 'Unknown error';
                showNotification('Failed to confirm reservation: ' + errorMessage, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to confirm reservation. Please try again.', 'error');
        });
    }
}

function cancelReservation(id) {
    if (confirm('Are you sure you want to cancel this reservation? This action cannot be undone.')) {
        fetch('/api/admin-confirm-reservation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reservation_id: id,
                action: 'cancel'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const message = data.data?.message || data.message || 'Reservation cancelled successfully.';
                showNotification(message, 'success');
                // Reload the page to update the status
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                const errorMessage = data.error || data.message || 'Unknown error';
                showNotification('Failed to cancel reservation: ' + errorMessage, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to cancel reservation. Please try again.', 'error');
        });
    }
}

function toggleServiceStatus(id) {
    showNotification('Toggle service status feature coming soon', 'info');
}

function exportServiceData(id) {
    showNotification('Export service data feature coming soon', 'info');
}

function duplicateCategory(id) {
    showNotification('Duplicate category feature coming soon', 'info');
}

function reorderCategory(id) {
    showNotification('Reorder category feature coming soon', 'info');
}

function exportCategoryData(id) {
    showNotification('Export category data feature coming soon', 'info');
}

function clearFilters() {
    // Clear all filter dropdowns
    const filterSelects = document.querySelectorAll('.filters-content select');
    filterSelects.forEach(select => {
        select.value = '';
    });

    // Clear search input
    const searchInput = document.querySelector('.search-bar input');
    if (searchInput) {
        searchInput.value = '';
    }

    // Trigger filter update
    showNotification('Filters cleared', 'success');

    // You can add actual filtering logic here
    // For now, just reload the page to reset filters
    setTimeout(() => {
        window.location.href = window.location.pathname;
    }, 500);
}

function applyFilter(filterType, value) {
    // Show loading overlay
    showLoadingOverlay('Applying filter...');

    const url = new URL(window.location);

    if (value) {
        url.searchParams.set(filterType, value);
    } else {
        url.searchParams.delete(filterType);
    }

    // Reset to first page when applying filters
    url.searchParams.set('page_num', '1');

    window.location.href = url.toString();
}

// Delete Functions
function deleteCustomer(id) {
    if (confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
        const formData = new FormData();
        formData.append('action', 'delete_customer');
        formData.append('id', id);

        fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Customer deleted successfully', 'success');
                    setTimeout(() => {
                        window.location.href = '/store-admin/?page=customers';
                    }, 1000);
                } else {
                    showNotification('Error deleting customer: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showNotification('Network error occurred', 'error');
            });
    }
}

function deleteService(id) {
    if (confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
        const formData = new FormData();
        formData.append('action', 'delete_service');
        formData.append('id', id);

        fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Service deleted successfully', 'success');
                    setTimeout(() => {
                        window.location.href = '/store-admin/?page=services';
                    }, 1000);
                } else {
                    showNotification('Error deleting service: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showNotification('Network error occurred', 'error');
            });
    }
}

function deleteEmployee(id) {
    if (confirm('Are you sure you want to delete this employee? This action cannot be undone.')) {
        const formData = new FormData();
        formData.append('action', 'delete_employee');
        formData.append('id', id);

        fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Employee deleted successfully', 'success');
                    setTimeout(() => {
                        window.location.href = '/store-admin/?page=employees';
                    }, 1000);
                } else {
                    showNotification('Error deleting employee: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showNotification('Network error occurred', 'error');
            });
    }
}

function deleteCategory(id) {
    if (confirm('Are you sure you want to delete this category? This will also affect all services in this category.')) {
        const formData = new FormData();
        formData.append('action', 'delete_category');
        formData.append('id', id);

        fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Category deleted successfully', 'success');
                    setTimeout(() => {
                        window.location.href = '/store-admin/?page=categories';
                    }, 1000);
                } else {
                    showNotification('Error deleting category: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showNotification('Network error occurred', 'error');
            });
    }
}

function deleteReservation(id) {
    if (confirm('Are you sure you want to delete this reservation? This action cannot be undone.')) {
        const formData = new FormData();
        formData.append('action', 'delete_reservation');
        formData.append('id', id);

        fetch('/store-admin/controllers/ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Reservation deleted successfully', 'success');
                    setTimeout(() => {
                        window.location.href = '/store-admin/?page=reservations';
                    }, 1000);
                } else {
                    showNotification('Error deleting reservation: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showNotification('Network error occurred', 'error');
            });
    }
}

// Make functions globally available
window.addCustomer = addCustomer;
window.editCustomer = editCustomer;
window.viewCustomer = viewCustomer;
window.addService = addService;
window.editService = editService;
window.viewService = viewService;
window.addEmployee = addEmployee;
window.editEmployee = editEmployee;
window.viewEmployee = viewEmployee;
window.addCategory = addCategory;
window.editCategory = editCategory;
window.viewCategory = viewCategory;
window.addReservation = addReservation;
window.editReservation = editReservation;
window.viewReservation = viewReservation;
window.bookAppointment = bookAppointment;
window.manageCategories = manageCategories;
window.manageSchedules = manageSchedules;
window.manageSchedule = manageSchedule;
window.reorderCategories = reorderCategories;
window.showCalendar = showCalendar;
window.duplicateService = duplicateService;
window.bulkUpdatePrices = bulkUpdatePrices;
window.bulkDuplicate = bulkDuplicate;
window.bulkToggleStatus = bulkToggleStatus;
window.bulkDelete = bulkDelete;
window.bulkEmail = bulkEmail;
window.bulkSMS = bulkSMS;
window.bulkExport = bulkExport;
window.bulkReorder = bulkReorder;
window.bulkUpdateColors = bulkUpdateColors;
window.bulkSchedule = bulkSchedule;
window.bulkNotify = bulkNotify;
window.bulkRemind = bulkRemind;
window.bulkReschedule = bulkReschedule;

window.showNotification = showNotification;

// Delete functions
window.deleteCustomer = deleteCustomer;
window.deleteService = deleteService;
window.deleteEmployee = deleteEmployee;
window.deleteCategory = deleteCategory;
window.deleteReservation = deleteReservation;

// Additional action functions
window.sendMessage = sendMessage;
window.sendEmail = sendEmail;
window.sendSMS = sendSMS;
window.sendReminder = sendReminder;
window.sendNotification = sendNotification;
window.rescheduleReservation = rescheduleReservation;
window.duplicateReservation = duplicateReservation;
window.toggleServiceStatus = toggleServiceStatus;
window.exportServiceData = exportServiceData;
window.duplicateCategory = duplicateCategory;
window.reorderCategory = reorderCategory;
window.exportCategoryData = exportCategoryData;
window.clearFilters = clearFilters;
window.applyFilter = applyFilter;
window.toggleFilters = toggleFilters;
window.toggleCategoryServices = toggleCategoryServices;
window.toggleEmployeeSkills = toggleEmployeeSkills;

function toggleFilters() {
    const filtersSection = document.getElementById('filters-section');
    const toggleBtn = document.getElementById('filter-toggle-btn');

    if (filtersSection.style.display === 'none') {
        filtersSection.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-filter"></i> Hide Filters';
        toggleBtn.classList.add('active');
    } else {
        filtersSection.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-filter"></i> Filters';
        toggleBtn.classList.remove('active');
    }
}

function toggleCategoryServices(categoryId) {
    const servicesSection = document.getElementById(`category-services-${categoryId}`);
    const arrow = document.getElementById(`category-arrow-${categoryId}`);

    if (servicesSection.style.display === 'none') {
        servicesSection.style.display = 'block';
        arrow.classList.remove('fa-chevron-down');
        arrow.classList.add('fa-chevron-up');
    } else {
        servicesSection.style.display = 'none';
        arrow.classList.remove('fa-chevron-up');
        arrow.classList.add('fa-chevron-down');
    }
}

function toggleEmployeeSkills(employeeId) {
    const skillsSection = document.getElementById(`employee-skills-${employeeId}`);
    const arrow = document.getElementById(`employee-arrow-${employeeId}`);

    if (skillsSection.style.display === 'none') {
        skillsSection.style.display = 'block';
        arrow.classList.remove('fa-chevron-down');
        arrow.classList.add('fa-chevron-up');
    } else {
        skillsSection.style.display = 'none';
        arrow.classList.remove('fa-chevron-up');
        arrow.classList.add('fa-chevron-down');
    }
}

// All CRUD functions registered globally

// Export data functionality
function handleExport() {
    const currentPage = new URLSearchParams(window.location.search).get('page') || 'customers';
    const cards = document.querySelectorAll('.entity-card');

    if (cards.length === 0) {
        showNotification('No data to export', 'warning');
        return;
    }

    const data = [];
    cards.forEach(card => {
        const rowData = extractCardData(card, currentPage);
        data.push(rowData);
    });

    // Convert to CSV
    const csv = convertToCSV(data);
    downloadCSV(csv, `${currentPage}_export_${new Date().toISOString().split('T')[0]}.csv`);

    showNotification(`Exported ${data.length} ${currentPage}`, 'success');
}

// Extract data from card for export
function extractCardData(card, entityType) {
    const data = {};

    // Basic info
    data.id = card.getAttribute('data-id');
    data.name = card.querySelector('.entity-title')?.textContent.trim() || '';

    // Extract info rows
    const infoRows = card.querySelectorAll('.info-row');
    infoRows.forEach(row => {
        const icon = row.querySelector('.info-icon i');
        const value = row.querySelector('.info-value')?.textContent.trim() || '';

        if (icon && value) {
            if (icon.classList.contains('fa-envelope')) data.email = value;
            if (icon.classList.contains('fa-phone')) data.phone = value;
            if (icon.classList.contains('fa-globe')) data.language = value;
            if (icon.classList.contains('fa-info-circle')) data.description = value;
        }
    });

    // Extract stats
    const statItems = card.querySelectorAll('.stat-item');
    statItems.forEach((item, index) => {
        const value = item.querySelector('.stat-value')?.textContent.trim() || '';
        const label = item.querySelector('.stat-label')?.textContent.trim() || `stat_${index}`;
        if (value) data[label.toLowerCase().replace(/\s+/g, '_')] = value;
    });

    // Extract status
    const statusBadge = card.querySelector('.status-badge');
    if (statusBadge) {
        data.status = statusBadge.textContent.trim();
    }

    return data;
}

/**
 * Searchable Dropdown Component
 * Transforms regular select elements into searchable dropdowns
 */
class SearchableDropdown {
    constructor(selectElement, options = {}) {
        this.select = selectElement;
        this.options = {
            maxVisible: options.maxVisible || 5,
            placeholder: options.placeholder || 'Search...',
            noResultsText: options.noResultsText || 'No results found',
            ...options
        };

        this.isOpen = false;
        this.selectedIndex = -1;
        this.filteredOptions = [];

        this.init();
    }

    init() {
        // Hide original select
        this.select.style.display = 'none';

        // Create wrapper
        this.wrapper = document.createElement('div');
        this.wrapper.className = 'searchable-dropdown';
        this.select.parentNode.insertBefore(this.wrapper, this.select);
        this.wrapper.appendChild(this.select);

        // Create display input
        this.input = document.createElement('input');
        this.input.type = 'text';
        this.input.className = 'searchable-input';
        this.input.placeholder = this.getSelectedText() || this.options.placeholder;
        this.input.autocomplete = 'off';
        this.input.style.textAlign = 'left';

        // Create dropdown arrow
        this.arrow = document.createElement('div');
        this.arrow.className = 'dropdown-arrow';
        this.arrow.innerHTML = '<i class="fas fa-chevron-down"></i>';

        // Create input wrapper
        this.inputWrapper = document.createElement('div');
        this.inputWrapper.className = 'input-wrapper';
        this.inputWrapper.appendChild(this.input);
        this.inputWrapper.appendChild(this.arrow);

        // Create dropdown list
        this.dropdown = document.createElement('div');
        this.dropdown.className = 'dropdown-list';

        // Add to wrapper
        this.wrapper.appendChild(this.inputWrapper);
        this.wrapper.appendChild(this.dropdown);

        // Bind events
        this.bindEvents();

        // Initialize options
        this.updateOptions();
    }

    bindEvents() {
        // Input events
        this.input.addEventListener('input', (e) => this.handleInput(e));
        this.input.addEventListener('focus', () => this.open());
        this.input.addEventListener('keydown', (e) => this.handleKeydown(e));

        // Arrow click
        this.arrow.addEventListener('click', () => this.toggle());

        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!this.wrapper.contains(e.target)) {
                this.close();
            }
        });

        // Original select change
        this.select.addEventListener('change', () => this.updateDisplay());
    }

    handleInput(e) {
        const query = e.target.value.toLowerCase();
        this.filterOptions(query);
        this.open();
    }

    handleKeydown(e) {
        if (!this.isOpen) {
            if (e.key === 'ArrowDown' || e.key === 'Enter') {
                e.preventDefault();
                this.open();
            }
            return;
        }

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, this.filteredOptions.length - 1);
                this.updateHighlight();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.updateHighlight();
                break;
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0) {
                    this.selectOption(this.filteredOptions[this.selectedIndex]);
                }
                break;
            case 'Escape':
                e.preventDefault();
                this.close();
                break;
        }
    }

    filterOptions(query) {
        const options = Array.from(this.select.options);
        this.filteredOptions = options.filter(option => {
            if (option.value === '') return query === ''; // Show empty option only when no query
            return option.textContent.toLowerCase().includes(query);
        });

        this.selectedIndex = -1;
        this.renderOptions();
    }

    renderOptions() {
        this.dropdown.innerHTML = '';

        if (this.filteredOptions.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'dropdown-item no-results';
            noResults.textContent = this.options.noResultsText;
            this.dropdown.appendChild(noResults);
            return;
        }

        // Show only first maxVisible options
        const visibleOptions = this.filteredOptions.slice(0, this.options.maxVisible);

        visibleOptions.forEach((option, index) => {
            const item = document.createElement('div');
            item.className = 'dropdown-item';
            item.textContent = option.textContent;
            item.dataset.value = option.value;

            item.addEventListener('click', () => this.selectOption(option));
            item.addEventListener('mouseenter', () => {
                this.selectedIndex = index;
                this.updateHighlight();
            });

            this.dropdown.appendChild(item);
        });

        // Show "more results" indicator if needed
        if (this.filteredOptions.length > this.options.maxVisible) {
            const moreIndicator = document.createElement('div');
            moreIndicator.className = 'dropdown-item more-indicator';
            moreIndicator.textContent = `+${this.filteredOptions.length - this.options.maxVisible} more results...`;
            this.dropdown.appendChild(moreIndicator);
        }
    }

    updateHighlight() {
        const items = this.dropdown.querySelectorAll('.dropdown-item:not(.no-results):not(.more-indicator)');
        items.forEach((item, index) => {
            item.classList.toggle('highlighted', index === this.selectedIndex);
        });
    }

    selectOption(option) {
        this.select.value = option.value;
        this.select.dispatchEvent(new Event('change', { bubbles: true }));
        this.updateDisplay();
        this.close();
    }

    updateDisplay() {
        const selectedText = this.getSelectedText();
        this.input.value = '';
        this.input.placeholder = selectedText || this.options.placeholder;
        this.input.style.textAlign = 'left';
    }

    getSelectedText() {
        const selectedOption = this.select.options[this.select.selectedIndex];
        return selectedOption && selectedOption.value ? selectedOption.textContent : '';
    }

    open() {
        if (this.isOpen) return;

        this.isOpen = true;
        this.wrapper.classList.add('open');
        this.filterOptions(this.input.value.toLowerCase());
        this.updateOptions();
    }

    close() {
        if (!this.isOpen) return;

        this.isOpen = false;
        this.wrapper.classList.remove('open');
        this.input.value = '';
        this.selectedIndex = -1;
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    updateOptions() {
        this.filterOptions('');
    }

    destroy() {
        this.wrapper.parentNode.insertBefore(this.select, this.wrapper);
        this.wrapper.remove();
        this.select.style.display = '';
    }
}

// Auto-initialize searchable dropdowns
document.addEventListener('DOMContentLoaded', function() {
    // Initialize searchable dropdowns for specific selects
    const searchableSelects = document.querySelectorAll('select[data-searchable]');
    searchableSelects.forEach(select => {
        new SearchableDropdown(select, {
            maxVisible: parseInt(select.dataset.maxVisible) || 5,
            placeholder: select.dataset.placeholder || 'Search...'
        });
    });
});

// Global function to initialize searchable dropdown
window.initSearchableDropdown = function(selector, options = {}) {
    const select = typeof selector === 'string' ? document.querySelector(selector) : selector;
    if (select) {
        return new SearchableDropdown(select, options);
    }
};

// Convert data to CSV
function convertToCSV(data) {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${(row[header] || '').toString().replace(/"/g, '""')}"`).join(','))
    ].join('\n');

    return csvContent;
}

// Download CSV file
function downloadCSV(csv, filename) {
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Handle import
function handleImport() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv,.xlsx,.xls';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            showNotification('Import functionality coming soon', 'info');
        }
    };
    input.click();
}

// Handle refresh
function handleRefresh() {
    showNotification('Refreshing data...', 'info');
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// Handle settings
function handleSettings() {
    const currentPage = new URLSearchParams(window.location.search).get('page') || 'customers';
    showNotification(`${currentPage.charAt(0).toUpperCase() + currentPage.slice(1)} settings coming soon`, 'info');
}
