/**
 * Booking System JavaScript
 * Handles the complete booking flow with multi-step process
 *
 * Steps:
 * 1. Select category
 * 2. Select service
 * 3. Select date
 * 4. Select time
 * 5. Enter customer details
 * 6. Confirm booking
 * 7. Verification
 */

class BookingSystem {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 7;

        // Initialize cache for API responses
        this.cache = new Map();
        this.verificationTimer = null;
        this.resendTimeout = 60;
        this.apiBase = '../api';
        this.verificationInProgress = false;
        this.verificationMethod = 'email'; // Will be loaded from settings

        // Initialize booking data with defaults
        const defaultBookingData = {
            category: null,
            service: null,
            date: null,
            time: null,
            employee: null,
            customer: {
                name: '',
                email: '',
                phone: ''
            },
            verificationCode: '',
            bookingId: null
        };

        // Try to restore booking data from sessionStorage (cache is now initialized)
        this.bookingData = this.restoreBookingData() || defaultBookingData;
    }

    // Save booking data to sessionStorage
    saveBookingData() {
        try {
            sessionStorage.setItem('bookingData', JSON.stringify(this.bookingData));

            // Also save cache data
            const cacheData = {};
            this.cache.forEach((value, key) => {
                cacheData[key] = value;
            });
            sessionStorage.setItem('bookingCache', JSON.stringify(cacheData));
        } catch (error) {
            console.error('Failed to save booking data:', error);
        }
    }

    // Restore booking data from sessionStorage
    restoreBookingData() {
        try {
            const saved = sessionStorage.getItem('bookingData');
            if (saved) {
                const bookingData = JSON.parse(saved);

                // Also restore cache data
                const cacheData = sessionStorage.getItem('bookingCache');
                if (cacheData) {
                    const parsedCache = JSON.parse(cacheData);
                    Object.entries(parsedCache).forEach(([key, value]) => {
                        this.cache.set(key, value);
                    });
                }

                return bookingData;
            }
        } catch (error) {
            console.error('Failed to restore booking data:', error);
        }
        return null;
    }

    // Clear booking data from sessionStorage
    clearBookingData() {
        try {
            sessionStorage.removeItem('bookingData');
            sessionStorage.removeItem('bookingCache');
        } catch (error) {
            console.error('Failed to clear booking data:', error);
        }
    }

    // Initialize the booking system
    init() {
        // Check for step parameter in URL (for language changes)
        const urlParams = new URLSearchParams(window.location.search);
        const stepParam = urlParams.get('step');

        // Also check sessionStorage for persisted step
        const sessionStep = sessionStorage.getItem('bookingCurrentStep');

        // Determine initial step (URL param takes priority, then sessionStorage, then default)
        let initialStep = 1;
        if (stepParam) {
            initialStep = parseInt(stepParam);
        } else if (sessionStep) {
            initialStep = parseInt(sessionStep);
        }

        // Validate step number
        if (initialStep >= 1 && initialStep <= this.totalSteps) {
            this.currentStep = initialStep;
        }

        // Store current step in sessionStorage
        sessionStorage.setItem('bookingCurrentStep', this.currentStep);

        // Load settings and then initialize
        this.loadSettings().then(() => {
            this.loadStep(this.currentStep);
            this.updateProgressBar();
        });
        this.setupEventListeners();

        // Clean up URL parameters after initialization
        if (stepParam) {
            const cleanUrl = new URL(window.location);
            cleanUrl.searchParams.delete('step');
            window.history.replaceState({step: this.currentStep}, '', cleanUrl.toString());
        }
    }

    // Load settings from API
    async loadSettings() {
        try {
            const response = await fetch(`${this.apiBase}/settings`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });

            if (response.ok) {
                const data = await response.json();
                this.bookingSettings = data.data || {};
                this.verificationMethod = this.bookingSettings.verification_method || 'email';
            }
        } catch (error) {
            console.warn('Could not fetch settings, using defaults:', error);
        }
    }

    // Setup global event listeners
    setupEventListeners() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.step) {
                this.currentStep = e.state.step;
                this.loadStep(this.currentStep);
                this.updateProgressBar();
            }
        });

        // Handle form submissions
        document.addEventListener('submit', (e) => {
            e.preventDefault();
        });

        // Handle verification code input
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('verification-digit')) {
                this.handleVerificationInput(e);
            }
        });

        // Handle paste events for verification code
        document.addEventListener('paste', (e) => {
            if (e.target.classList.contains('verification-digit')) {
                this.handleVerificationPaste(e);
            }
        });

        // Handle keydown for backspace navigation
        document.addEventListener('keydown', (e) => {
            if (e.target.classList.contains('verification-digit')) {
                this.handleVerificationKeydown(e);
            }
        });

        // Handle keyboard navigation (disabled for now to prevent auto-advancing)
        // document.addEventListener('keydown', (e) => {
        //     if (e.key === 'Enter' && !e.target.matches('input, textarea')) {
        //         e.preventDefault();
        //         this.nextStep();
        //     }
        // });
    }

    // Load a specific step
    async loadStep(stepNumber) {

        this.showLoading();

        try {
            const content = await this.getStepContent(stepNumber);
            document.getElementById('stepContent').innerHTML = content;
            this.updateNavigationButtons();
            this.updateProgressBar();
            this.hideLoading();

            // Add event listeners for step 5 form fields
            if (stepNumber === 5) {
                this.setupStep5EventListeners();
            }

            // Add fade-in animation
            document.getElementById('stepContent').classList.add('fade-in');
            
            // Update browser history while preserving existing URL parameters
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('step', stepNumber);
            history.pushState({ step: stepNumber }, '', currentUrl.toString());
            
        } catch (error) {
            console.error('Error loading step:', error);
            this.showError('Failed to load step. Please try again.');
            this.hideLoading();
        }
    }

    // Get content for a specific step
    async getStepContent(stepNumber) {
        switch (stepNumber) {
            case 1:
                return await this.getCategoryStep();
            case 2:
                return await this.getServiceStep();
            case 3:
                return await this.getDateStep();
            case 4:
                return await this.getTimeStep();
            case 5:
                return this.getDetailsStep();
            case 6:
                return this.getVerificationStep();
            case 7:
                return this.getConfirmationStep();
            default:
                throw new Error('Invalid step number');
        }
    }

    // Step 1: Category selection
    async getCategoryStep() {
        const categories = await this.fetchCategories();

        if (!categories || !Array.isArray(categories)) {
            throw new Error('No categories available');
        }

        let html = `
            <div class="step-title">${t('select_category', 'Επιλέξτε Κατηγορία')}</div>
            <div class="step-subtitle">${t('select_category_subtitle', 'Επιλέξτε τον τύπο υπηρεσίας που αναζητάτε')}</div>
            <div class="category-grid">
        `;

        categories.forEach(category => {
            // Use translation for category name
            const categoryName = t(`category_name_${category.id}`, category.name);
            const categoryDescription = t(`category_description_${category.id}`, category.description || '');

            html += `
                <div class="category-card" data-category-id="${category.id}" onclick="BookingSystem.instance.selectCategory('${category.id}')">
                    <div class="category-icon" style="color: ${category.color}"><i class="${category.icon}"></i></div>
                    <div class="category-name">${categoryName}</div>
                    <div class="category-description">${categoryDescription}</div>
                </div>
            `;
        });
        
        html += `</div>`;
        return html;
    }

    // Step 2: Service selection
    async getServiceStep() {
        if (!this.bookingData.category) {
            this.goToStep(1);
            return;
        }

        const services = await this.fetchServices(this.bookingData.category);
        
        let html = `
            <div class="step-title">${t('select_service', 'Επιλέξτε Υπηρεσία')}</div>
            <div class="step-subtitle">${t('select_service_subtitle', 'Επιλέξτε την υπηρεσία που θέλετε να κλείσετε')}</div>
            <div class="service-grid">
        `;
        
        services.forEach(service => {
            const isActive = service.is_active == 1;
            const cardClass = `service-card ${!isActive ? 'disabled' : ''}`;
            const onclick = isActive ? `onclick="BookingSystem.instance.selectService('${service.id}')"` : '';

            // Use translation for service name and description
            const serviceName = t(`service_name_${service.id}`, service.name);
            const serviceDescription = t(`service_description_${service.id}`, service.description);

            html += `
                <div class="${cardClass}" data-service-id="${service.id}" ${onclick}>
                    <div class="service-header">
                        <div class="service-name">${serviceName}</div>
                        <div class="service-price">€${parseFloat(service.price).toFixed(2)}</div>
                        ${!isActive ? `<div class="service-status">${t('unavailable', 'Μη διαθέσιμο')}</div>` : ''}
                    </div>
                    <div class="service-description">${serviceDescription}</div>
                    <div class="service-duration">
                        <i class="fas fa-clock"></i>
                        <span>${service.duration} ${t('minutes', 'λεπτά')}</span>
                    </div>
                </div>
            `;
        });
        
        html += `</div>`;
        return html;
    }

    // Step 3: Date selection
    async getDateStep() {

        if (!this.bookingData.service) {

            this.goToStep(2);
            return;
        }

        const currentDate = new Date();
        this.currentCalendarDate = currentDate; // Store for navigation

        const availability = await this.fetchAvailability(this.bookingData.service, currentDate);

        let html = `
            <div class="step-title">${t('select_date', 'Επιλέξτε Ημερομηνία')}</div>
            <div class="step-subtitle">${t('select_date_subtitle', 'Επιλέξτε την προτιμώμενη ημερομηνία')}</div>
            <div class="calendar-container">
                <div class="calendar-header">
                    <div class="calendar-month" id="calendarMonth"></div>
                    <div class="calendar-nav">
                        <button class="calendar-nav-btn" onclick="BookingSystem.instance.changeMonth(-1)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="calendar-nav-btn" onclick="BookingSystem.instance.changeMonth(1)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                <div class="calendar-grid" id="calendarGrid">
                    ${await this.generateCalendar(currentDate, availability)}
                </div>
            </div>
        `;
        
        return html;
    }

    // Step 4: Time selection
    async getTimeStep() {
        if (!this.bookingData.date) {
            this.goToStep(3);
            return;
        }

        const timeSlots = await this.fetchTimeSlots(this.bookingData.service, this.bookingData.date);
        const serviceData = await this.getServiceData(this.bookingData.service);

        // Check if this service requires manual employee selection
        const isManualSelection = serviceData.employee_selection === 'manual';

        let html = `
            <div class="step-title">${isManualSelection ? t('select_time_staff', 'Select Time & Staff') : t('select_time', 'Select Time')}</div>
            <div class="step-subtitle">${isManualSelection ? t('choose_preferred_time_staff', 'Choose your preferred time slot and staff member') : t('choose_preferred_time', 'Choose your preferred time slot')}</div>
            <div class="time-slots-container">
                <div class="time-slots-header">
                    <div class="selected-date">${this.formatDate(this.bookingData.date)}</div>
                    <div class="selected-service">${serviceData.name} (${serviceData.duration} ${t('minutes', 'λεπτά')})</div>
                </div>
        `;

        // Add employee map for manual selection services
        if (isManualSelection) {

            const employees = await this.getEmployeeMapForService(this.bookingData.service, this.bookingData.date);

            // Fetch detailed employee availability for all time slots (cache for fast filtering)

            this.employeeTimeSlotCache = {};

            for (const [timeStr, slotData] of Object.entries(timeSlots)) {
                if (slotData.available) {
                    this.employeeTimeSlotCache[timeStr] = await this.getAvailableEmployeesForTimeSlot(
                        serviceData.id,
                        this.bookingData.date,
                        timeStr
                    );
                } else {
                    this.employeeTimeSlotCache[timeStr] = [];
                }
            }

            // Check which employees have available slots using cached data
            for (const employee of employees) {
                employee.hasAvailableSlots = this.checkEmployeeHasAvailableSlotsFromCache(employee.id);
            }

            // Initialize with auto selection and store employees for filtering
            this.selectedEmployeeFilter = 'auto';
            this.availableEmployees = employees;
            this.cachedTimeSlots = timeSlots;
            this.cachedServiceData = serviceData;

            html += this.renderEmployeeMap(employees);
        } else {

        }

        html += `<div class="time-slots-grid">`;

        // Render time slots based on service type
        if (isManualSelection) {
            // For manual selection services, render filtered time slots

            html += await this.renderManualTimeSlots(timeSlots, serviceData);
        } else {
            // For auto selection services, render regular time slots

            html += this.renderAutoTimeSlots(timeSlots);
        }
        
        html += `
                </div>
            </div>
        `;
        
        return html;
    }

    // Step 5: Customer details
    getDetailsStep() {
        const { name, email, phone } = this.bookingData.customer;
        const isReturningCustomer = this.bookingData.isReturningCustomer || false;
        const verificationMethod = this.verificationMethod || 'email';

        return `
            <div class="step-title">${t('contact_details', 'Στοιχεία Επικοινωνίας')}</div>
            <div class="step-subtitle">${t('contact_details_subtitle', 'Παρακαλώ παρέχετε τα στοιχεία επικοινωνίας σας')}</div>

            <!-- Customer Type Toggle -->
            <div class="customer-type-toggle">
                <button type="button" id="customerTypeToggle" class="toggle-button ${isReturningCustomer ? 'returning' : 'new'}">
                    <span class="toggle-text">
                        ${isReturningCustomer
                            ? t('first_time_here', 'Πρώτη φορά εδώ;')
                            : t('already_customer', 'Ήδη πελάτης;')
                        }
                    </span>
                </button>
            </div>

            <div class="form-container">
                <form id="customerForm">
                    ${isReturningCustomer ? this.getReturningCustomerForm(email, verificationMethod) : this.getNewCustomerForm(name, email, phone)}
                </form>
            </div>
        `;
    }

    getNewCustomerForm(name, email, phone) {
        return `
            <div class="form-group">
                <label class="form-label" for="customerName">${t('name', 'Όνομα')} *</label>
                <input type="text" id="customerName" name="name" class="form-input"
                       value="${name}" required placeholder="${t('enter_full_name', 'Εισάγετε το πλήρες όνομά σας')}">
                <div class="form-error" id="nameError"></div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label" for="customerEmail">${t('email', 'Email')} *</label>
                    <input type="email" id="customerEmail" name="email" class="form-input"
                           value="${email}" required placeholder="<EMAIL>">
                    <div class="form-error" id="emailError"></div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="customerPhone">${t('phone', 'Τηλέφωνο')} ${this.bookingSettings?.require_phone ? '*' : ''}</label>
                    <input type="tel" id="customerPhone" name="phone" class="form-input"
                           value="${phone}" ${this.bookingSettings?.require_phone ? 'required' : ''} placeholder="69XXXXXXXX">
                    <div class="form-error" id="phoneError"></div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="customerNotes">${t('additional_notes', 'Επιπλέον Σημειώσεις')} (${t('optional', 'Προαιρετικό')})</label>
                <textarea id="customerNotes" name="notes" class="form-input"
                          placeholder="${t('special_requests', 'Οποιαδήποτε ειδικά αιτήματα ή σημειώσεις...')}" rows="3"></textarea>
            </div>
        `;
    }

    getReturningCustomerForm(email, verificationMethod) {
        const fieldLabel = verificationMethod === 'sms'
            ? t('phone', 'Τηλέφωνο')
            : t('email', 'Email');
        const fieldType = verificationMethod === 'sms' ? 'tel' : 'email';
        const fieldName = verificationMethod === 'sms' ? 'phone' : 'email';
        const placeholder = verificationMethod === 'sms' ? '69XXXXXXXX' : '<EMAIL>';

        return `
            <div class="returning-customer-notice">
                <i class="fas fa-info-circle"></i>
                <span>${t('returning_customer_notice', 'Θα χρησιμοποιήσουμε τα στοιχεία σας από την προηγούμενη κράτηση')}</span>
            </div>

            <div class="form-group">
                <label class="form-label" for="customer${verificationMethod === 'sms' ? 'Phone' : 'Email'}">${fieldLabel} *</label>
                <input type="${fieldType}" id="customer${verificationMethod === 'sms' ? 'Phone' : 'Email'}" name="${fieldName}" class="form-input"
                       value="${email}" required placeholder="${placeholder}">
                <div class="form-error" id="${fieldName}Error"></div>
            </div>

            <div class="customer-lookup-status" id="customerLookupStatus" style="display: none;">
                <div class="lookup-loading" id="lookupLoading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>${t('looking_up_customer', 'Αναζήτηση πελάτη...')}</span>
                </div>
                <div class="lookup-found" id="lookupFound" style="display: none;">
                    <i class="fas fa-check-circle"></i>
                    <span>${t('customer_found', 'Βρέθηκε πελάτης:')} <strong id="foundCustomerName"></strong></span>
                </div>
                <div class="lookup-not-found" id="lookupNotFound" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${t('customer_not_found', 'Δεν βρέθηκε πελάτης με αυτό το email')}</span>
                </div>
            </div>
        `;
    }

    // Step 6: Verification (Email or SMS)
    getVerificationStep() {
        const isEmailVerification = this.verificationMethod === 'email';
        const isSMSVerification = this.verificationMethod === 'sms';

        const icon = isEmailVerification ? 'fas fa-envelope' : 'fas fa-mobile-alt';
        const title = isEmailVerification
            ? t('verify_email', 'Επιβεβαιώστε το Email σας')
            : t('verify_phone', 'Επιβεβαιώστε το Τηλέφωνό σας');
        const target = isEmailVerification
            ? this.bookingData.customer.email
            : this.bookingData.customer.phone;
        const message = isEmailVerification
            ? t('verification_code_sent_to_email', 'Στείλαμε κωδικό επιβεβαίωσης στο email')
            : t('verification_code_sent_to_phone', 'Στείλαμε κωδικό επιβεβαίωσης στο τηλέφωνο');

        return `
            <div class="verification-container">
                <div class="verification-icon">
                    <i class="${icon}"></i>
                </div>
                <div class="step-title">${title}</div>
                <div class="verification-message">
                    ${message}<br>
                    <strong>${target}</strong>
                </div>
                <div class="verification-code">
                    <input type="text" class="verification-digit" maxlength="1" data-index="0">
                    <input type="text" class="verification-digit" maxlength="1" data-index="1">
                    <input type="text" class="verification-digit" maxlength="1" data-index="2">
                    <input type="text" class="verification-digit" maxlength="1" data-index="3">
                </div>
                <div class="verification-error" id="verificationError" style="display: none;">
                    <i class="fas fa-exclamation-circle"></i>
                    <span id="verificationErrorText"></span>
                </div>
                <div class="text-center">
                    <p>${t('didnt_receive_code', 'Δεν λάβατε τον κωδικό;')}</p>
                    <a href="#" class="resend-code" onclick="BookingSystem.instance.resendVerificationCode()">
                        ${t('resend_code', 'Επαναποστολή Κωδικού')}
                    </a>
                    <div class="resend-timer" id="resendTimer"></div>
                </div>
            </div>
        `;
    }

    // Step 7: Confirmation
    async getConfirmationStep() {
        const serviceData = await this.getServiceData(this.bookingData.service);
        const categoryData = await this.getCategoryData(this.bookingData.category);

        // Determine confirmation status based on booking confirmation setting
        // Default to 'auto' for backward compatibility with existing tenants
        const bookingConfirmation = this.bookingSettings?.booking_confirmation || 'auto';
        const isAutoConfirm = bookingConfirmation === 'auto';

        const confirmationIcon = isAutoConfirm ? 'fas fa-check-circle' : 'fas fa-clock';
        const confirmationTitle = isAutoConfirm
            ? t('booking_confirmed', 'Το ραντεβού επιβεβαιώθηκε!')
            : t('booking_pending', 'Το ραντεβού σας υποβλήθηκε!');

        const confirmationMessage = isAutoConfirm
            ? t('booking_success_message', 'Το ραντεβού σας κλείστηκε επιτυχώς.<br>Θα λάβετε email επιβεβαίωσης σύντομα.')
            : t('booking_pending_message', 'Το ραντεβού σας υποβλήθηκε επιτυχώς και αναμένει επιβεβαίωση από τον διαχειριστή.<br>Θα ενημερωθείτε μόλις επιβεβαιωθεί.');

        return `
            <div class="confirmation-container">
                <div class="confirmation-icon ${isAutoConfirm ? 'confirmed' : 'pending'}">
                    <i class="${confirmationIcon}"></i>
                </div>
                <div class="confirmation-title">${confirmationTitle}</div>
                <div class="confirmation-message">
                    ${confirmationMessage}
                </div>
                
                <div class="booking-summary">
                    <div class="booking-id">
                        ${t('booking_id', 'Κωδικός Κράτησης')}: ${this.bookingData.bookingId || t('pending', 'ΕΚΚΡΕΜΕΙ')}
                    </div>

                    <div class="summary-item">
                        <span class="summary-label">${t('service', 'Υπηρεσία')}:</span>
                        <span class="summary-value">${serviceData.name}</span>
                    </div>

                    <div class="summary-item">
                        <span class="summary-label">${t('category', 'Κατηγορία')}:</span>
                        <span class="summary-value">${categoryData.name}</span>
                    </div>

                    <div class="summary-item">
                        <span class="summary-label">${t('date', 'Ημερομηνία')}:</span>
                        <span class="summary-value">${this.formatDate(this.bookingData.date)}</span>
                    </div>

                    <div class="summary-item">
                        <span class="summary-label">${t('time', 'Ώρα')}:</span>
                        <span class="summary-value">${this.bookingData.time}</span>
                    </div>

                    <div class="summary-item">
                        <span class="summary-label">${t('duration', 'Διάρκεια')}:</span>
                        <span class="summary-value">${serviceData.duration} ${t('minutes', 'λεπτά')}</span>
                    </div>

                    <div class="summary-item">
                        <span class="summary-label">${t('customer', 'Πελάτης')}:</span>
                        <span class="summary-value">${this.bookingData.customer.name}</span>
                    </div>

                    <div class="summary-item">
                        <span class="summary-label">${t('email', 'Email')}:</span>
                        <span class="summary-value">${this.bookingData.customer.email}</span>
                    </div>

                    <div class="summary-item">
                        <span class="summary-label">${t('phone', 'Τηλέφωνο')}:</span>
                        <span class="summary-value">${this.bookingData.customer.phone}</span>
                    </div>

                    <div class="summary-item">
                        <span class="summary-label">${t('total_price', 'Συνολική Τιμή')}:</span>
                        <span class="summary-price">€${parseFloat(serviceData.price).toFixed(2)}</span>
                    </div>
                </div>
                
                <div class="text-center">
                    <button class="btn btn-primary" onclick="BookingSystem.instance.newBooking()">
                        <i class="fas fa-plus"></i>
                        ${t('book_another_appointment', 'Book Another Appointment')}
                    </button>
                </div>
            </div>
        `;
    }

    // Selection handlers
    selectCategory(categoryId) {

        // Clear previous booking data when selecting a new category
        this.bookingData = {
            category: categoryId,
            service: null,
            date: null,
            time: null,
            employee: null,
            customer: {
                name: '',
                email: '',
                phone: ''
            },
            verificationCode: '',
            bookingId: null
        };

        // Save booking data to sessionStorage
        this.saveBookingData();

        document.querySelectorAll('.category-card').forEach(card => {
            card.classList.remove('selected');
        });
        document.querySelector(`[data-category-id="${categoryId}"]`).classList.add('selected');

        // Update navigation buttons
        this.updateNavigationButtons();

        setTimeout(async () => await this.nextStep(), 300);
    }

    selectService(serviceId) {

        this.bookingData.service = serviceId;
        this.saveBookingData(); // Save after updating service

        document.querySelectorAll('.service-card').forEach(card => {
            card.classList.remove('selected');
        });
        document.querySelector(`[data-service-id="${serviceId}"]`).classList.add('selected');

        // Update navigation buttons
        this.updateNavigationButtons();

        setTimeout(async () => await this.nextStep(), 300);
    }

    selectDate(dateStr) {
        this.bookingData.date = dateStr;
        this.saveBookingData(); // Save after updating date
        document.querySelectorAll('.calendar-day').forEach(day => {
            day.classList.remove('selected');
        });
        document.querySelector(`[data-date="${dateStr}"]`).classList.add('selected');

        // Update navigation buttons
        this.updateNavigationButtons();

        setTimeout(async () => await this.nextStep(), 300);
    }

    selectTime(time, employees = []) {
        this.bookingData.time = time;
        this.bookingData.employees = employees;

        // Auto-assign employee if needed
        if (employees && employees.length === 1) {
            this.bookingData.employee = employees[0].id;
        }

        this.saveBookingData(); // Save after updating time

        document.querySelectorAll('.time-slot').forEach(slot => {
            slot.classList.remove('selected');
        });

        // Find the clicked time slot and mark it as selected
        const timeSlots = document.querySelectorAll('.time-slot');
        timeSlots.forEach(slot => {
            if (slot.textContent.trim().startsWith(time)) {
                slot.classList.add('selected');
            }
        });

        setTimeout(async () => await this.nextStep(), 300);
    }

    // Navigation methods
    async nextStep() {
        if (this.currentStep < this.totalSteps) {
            if (await this.validateCurrentStep()) {
                this.currentStep++;

                // Skip verification step (step 6) if verification method is 'none'
                if (this.currentStep === 6 && this.verificationMethod === 'none') {
                    this.currentStep = 7; // Jump directly to confirmation
                }

                sessionStorage.setItem('bookingCurrentStep', this.currentStep);
                this.loadStep(this.currentStep);
            }
        }
    }

    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;

            // Skip verification step (step 6) if verification method is 'none'
            if (this.currentStep === 6 && this.verificationMethod === 'none') {
                this.currentStep = 5; // Go back to contact details
            }

            sessionStorage.setItem('bookingCurrentStep', this.currentStep);
            this.loadStep(this.currentStep);
        }
    }

    goToStep(stepNumber) {
        if (stepNumber >= 1 && stepNumber <= this.totalSteps) {
            this.currentStep = stepNumber;
            sessionStorage.setItem('bookingCurrentStep', this.currentStep);
            this.loadStep(this.currentStep);
        }
    }

    // Validation
    async validateCurrentStep() {
        switch (this.currentStep) {
            case 1:
                return this.bookingData.category !== null;
            case 2:
                return this.bookingData.service !== null;
            case 3:
                return this.bookingData.date !== null;
            case 4:
                return this.bookingData.time !== null;
            case 5:
                return await this.validateCustomerDetails();
            case 6:
                return this.validateVerificationCode();
            default:
                return true;
        }
    }

    async validateCustomerDetails() {
        const form = document.getElementById('customerForm');
        if (!form) return false;

        const formData = new FormData(form);
        const isReturningCustomer = this.bookingData.isReturningCustomer || false;
        let isValid = true;

        // Clear previous errors
        document.querySelectorAll('.form-error').forEach(error => error.textContent = '');
        document.querySelectorAll('.form-input').forEach(input => input.classList.remove('error'));

        if (isReturningCustomer) {
            // For returning customers, only validate the verification field
            if (this.verificationMethod === 'sms') {
                const phone = formData.get('phone')?.trim() || '';
                if (!phone) {
                    this.showFieldError('phoneError', 'customerPhone', t('phone_required', 'Το τηλέφωνο είναι υποχρεωτικό'));
                    isValid = false;
                } else if (!this.isValidPhone(phone)) {
                    this.showFieldError('phoneError', 'customerPhone', t('invalid_phone', 'Παρακαλώ εισάγετε έγκυρο ελληνικό τηλέφωνο'));
                    isValid = false;
                } else {
                    this.bookingData.customer.phone = phone;
                }
            } else {
                const email = formData.get('email')?.trim() || '';
                if (!email) {
                    this.showFieldError('emailError', 'customerEmail', t('email_required', 'Το email είναι υποχρεωτικό'));
                    isValid = false;
                } else if (!this.isValidEmail(email)) {
                    this.showFieldError('emailError', 'customerEmail', t('invalid_email', 'Παρακαλώ εισάγετε έγκυρο email'));
                    isValid = false;
                } else {
                    this.bookingData.customer.email = email;
                }
            }

            // For returning customers, ensure we have customer name (from lookup)
            if (isValid && (!this.bookingData.customer.name || this.bookingData.customer.name.trim() === '')) {
                // Customer not found in lookup, show error
                const fieldName = this.verificationMethod === 'sms' ? 'phoneError' : 'emailError';
                const fieldId = this.verificationMethod === 'sms' ? 'customerPhone' : 'customerEmail';
                this.showFieldError(fieldName, fieldId, t('customer_not_found_error', 'Customer not found. Please check your details or use "First time here?" option.'));
                isValid = false;
            }
        } else {
            // For new customers, validate all required fields
            const name = formData.get('name')?.trim() || '';
            const email = formData.get('email')?.trim() || '';
            const phone = formData.get('phone')?.trim() || '';
            const notes = formData.get('notes')?.trim() || '';

            // Validate name
            if (!name) {
                this.showFieldError('nameError', 'customerName', t('name_required', 'Το όνομα είναι υποχρεωτικό'));
                isValid = false;
            } else if (name.length < 2) {
                this.showFieldError('nameError', 'customerName', t('name_min_length', 'Το όνομα πρέπει να έχει τουλάχιστον 2 χαρακτήρες'));
                isValid = false;
            }

            // Validate email
            if (!email) {
                this.showFieldError('emailError', 'customerEmail', t('email_required', 'Το email είναι υποχρεωτικό'));
                isValid = false;
            } else if (!this.isValidEmail(email)) {
                this.showFieldError('emailError', 'customerEmail', t('invalid_email', 'Παρακαλώ εισάγετε έγκυρο email'));
                isValid = false;
            }

            // Validate phone (only if required by settings)
            const phoneRequired = this.bookingSettings?.require_phone === true;
            if (phoneRequired && !phone) {
                this.showFieldError('phoneError', 'customerPhone', t('phone_required', 'Το τηλέφωνο είναι υποχρεωτικό'));
                isValid = false;
            } else if (phone && !this.isValidPhone(phone)) {
                this.showFieldError('phoneError', 'customerPhone', t('invalid_phone', 'Παρακαλώ εισάγετε έγκυρο ελληνικό τηλέφωνο'));
                isValid = false;
            }

            if (isValid) {
                this.bookingData.customer = { name, email, phone, notes };
            }
        }

        if (isValid) {
            this.saveBookingData(); // Save after updating customer details
            await this.createBooking();
        }

        return isValid;
    }

    validateVerificationCode() {
        // Prevent double validation
        if (this.verificationInProgress) {
            return false;
        }

        const digits = document.querySelectorAll('.verification-digit');
        const code = Array.from(digits).map(digit => digit.value).join('');

        if (code.length !== 4) {
            this.showVerificationError('Please enter the complete verification code');
            return false;
        }

        this.bookingData.verificationCode = code;
        this.saveBookingData(); // Save after updating verification code
        return this.verifyCode(code);
    }

    // Verification methods

    async verifyCode(code) {
        // Prevent double verification
        if (this.verificationInProgress) {
            return false;
        }

        this.verificationInProgress = true;

        try {
            const requestData = {
                reservation_id: this.bookingData.bookingId,
                verification_code: code,
                action: 'verify'
            };

            const response = await fetch(`${this.apiBase}/verification`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            const data = await response.json();
            if (data.success) {
                this.hideVerificationError();
                this.nextStep(); // Move to confirmation step instead of creating booking again
                return true;
            } else {
                this.showVerificationError(data.error || 'Invalid verification code');
                return false;
            }
        } catch (error) {
            console.error('Verification error:', error);
            this.showVerificationError('Failed to verify code. Please try again.');
            return false;
        } finally {
            this.verificationInProgress = false;
        }
    }

    async resendVerificationCode() {
        try {
            const response = await fetch(`${this.apiBase}/verification`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'resend',
                    reservation_id: this.bookingData.bookingId
                })
            });

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error || 'Failed to resend verification code');
            }

            this.showSuccess('Verification code resent successfully');
            this.startResendTimer();
        } catch (error) {
            console.error('Resend error:', error);
            this.showError('Failed to resend verification code. Please try again.');
        }
    }

    // Booking creation
    async createBooking() {
        try {
            // Get current language from multiple sources with priority order
            const urlParams = new URLSearchParams(window.location.search);
            const langFromUrl = urlParams.get('lang');
            const langFromSession = sessionStorage.getItem('selectedLanguage');
            const langFromLocal = localStorage.getItem('language');

            // Priority: URL > Session > Local > Default (Greek)
            let currentLanguage = 'el'; // Default to Greek

            if (langFromUrl && (langFromUrl === 'en' || langFromUrl === 'el')) {
                currentLanguage = langFromUrl;
            } else if (langFromSession && (langFromSession === 'en' || langFromSession === 'el')) {
                currentLanguage = langFromSession;
            } else if (langFromLocal && (langFromLocal === 'en' || langFromLocal === 'el')) {
                currentLanguage = langFromLocal;
            }

            // Ensure the detected language is stored for consistency
            if (currentLanguage && !langFromSession) {
                sessionStorage.setItem('selectedLanguage', currentLanguage);
            }
            if (currentLanguage && !langFromLocal) {
                localStorage.setItem('language', currentLanguage);
            }

            // Debug logging to console
            console.log('🌐 Language Detection Debug:', {
                fromUrl: langFromUrl,
                fromSession: langFromSession,
                fromLocal: langFromLocal,
                final: currentLanguage,
                timestamp: new Date().toISOString()
            });



            const bookingPayload = {
                service_id: this.bookingData.service,
                employee_id: this.bookingData.employee,
                date: this.bookingData.date,
                time: this.bookingData.time,
                customer: this.bookingData.customer,
                verification_code: this.bookingData.verificationCode,
                language: currentLanguage
            };

            // Debug logging for booking payload
            console.log('📤 Booking Payload Debug:', {
                payload: bookingPayload,
                language_specifically: currentLanguage,
                timestamp: new Date().toISOString()
            });

            const response = await fetch(`${this.apiBase}/booking`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(bookingPayload)
            });

            // Get response text first to see what we're actually receiving
            const responseText = await response.text();

            if (!responseText) {
                throw new Error('Empty response from server');
            }

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('JSON parse error:', parseError);
                console.error('Response text that failed to parse:', responseText);
                throw new Error(`Invalid JSON response: ${responseText.substring(0, 200)}`);
            }

            if (data.success) {
                this.bookingData.bookingId = data.data.reservation_id;
                this.saveBookingData(); // Save after getting booking ID
                return true;
            } else {
                throw new Error(data.error || 'Failed to create booking');
            }
        } catch (error) {
            console.error('Booking error:', error);

            // Check if it's a time conflict error
            const errorMessage = error.message || 'Failed to create booking. Please try again.';

            if (errorMessage.includes('reserved by another customer') ||
                errorMessage.includes('κρατηθεί από άλλον πελάτη') ||
                errorMessage.includes('time slot') ||
                errorMessage.includes('no longer available')) {

                // Show error and redirect back to time selection
                this.showError(errorMessage);

                // Wait a moment then redirect to time selection
                setTimeout(() => {
                    this.goToStep(4); // Go back to time selection step
                }, 3000);

            } else {
                this.showError(errorMessage);
            }

            return false;
        }
    }

    // API methods
    async fetchCategories() {
        const cacheKey = 'categories';
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            const response = await fetch(`${this.apiBase}/categories`);
            const data = await response.json();
            
            if (data.success) {
                this.cache.set(cacheKey, data.data);
                return data.data;
            } else {
                throw new Error(data.error || 'Failed to fetch categories');
            }
        } catch (error) {
            console.error('Categories fetch error:', error);
            throw error;
        }
    }

    async fetchServices(categoryId) {

        const cacheKey = `services_${categoryId}`;
        if (this.cache.has(cacheKey)) {

            return this.cache.get(cacheKey);
        }

        try {
            const url = `${this.apiBase}/services?category_id=${categoryId}`;

            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {

                this.cache.set(cacheKey, data.data);
                return data.data;
            } else {
                throw new Error(data.error || 'Failed to fetch services');
            }
        } catch (error) {
            console.error('Services fetch error:', error);
            throw error;
        }
    }

    async fetchAvailability(serviceId, date) {

        if (!serviceId || !date) {
            throw new Error('Missing required parameters: service_id, date');
        }

        const year = date.getFullYear();
        const month = date.getMonth() + 1; // JavaScript months are 0-based

        const cacheKey = `availability_${serviceId}_${year}_${month}`;

        if (this.cache.has(cacheKey)) {

            return this.cache.get(cacheKey);
        }

        try {
            const url = `${this.apiBase}/availability?service_id=${serviceId}&year=${year}&month=${month}`;

            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            // Check for error response
            if (data.error) {
                throw new Error(data.error);
            }

            // Check for success response
            if (data.success) {
                // The API returns {success: true, data: {availability: {"2025-07-07": true, ...}}}
                const rawAvailability = data.data.availability || {};

                // Convert API format to expected format if needed
                const availability = {};
                for (const [date, value] of Object.entries(rawAvailability)) {
                    if (typeof value === 'boolean') {
                        // New format: {"2025-07-07": true}
                        availability[date] = value;
                    } else if (typeof value === 'object' && value.hasOwnProperty('available')) {
                        // Old format: {"2025-07-07": {"available": true, "slots": 8}}
                        availability[date] = value.available;
                    } else {
                        // Fallback
                        availability[date] = false;
                    }
                }

                this.cache.set(cacheKey, availability);
                return availability;
            } else {
                throw new Error('Invalid API response format');
            }
        } catch (error) {
            console.error('Availability fetch error:', error);
            throw error;
        }
    }

    async fetchTimeSlots(serviceId, date) {

        const cacheKey = `timeslots_${serviceId}_${date}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            const url = `${this.apiBase}/timeslots?service_id=${serviceId}&date=${date}`;

            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            // Check for error response
            if (data.error) {
                throw new Error(data.error);
            }

            if (data.success) {
                // Extract detailed slots object from the response
                const detailedSlots = data.data.slots || {};

                this.cache.set(cacheKey, detailedSlots);
                return detailedSlots;
            } else {
                throw new Error('Invalid API response format');
            }
        } catch (error) {
            console.error('Time slots fetch error:', error);
            throw error;
        }
    }

    async getServiceData(serviceId) {

        const cacheKey = `service_${serviceId}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        // Service IDs are strings, not numbers - compare directly

        // Try to get from services cache first
        for (const [key, value] of this.cache.entries()) {
            if (key.startsWith('services_')) {

                const service = value.find(s => {

                    return s.id === serviceId;
                });
                if (service) {

                    this.cache.set(cacheKey, service);
                    return service;
                }
            }
        }

        throw new Error('Service data not found');
    }

    // Get employee availability map for a service on a specific date
    async getEmployeeMapForService(serviceId, date) {
        try {
            const url = `${this.apiBase}/employees?service_id=${serviceId}&date=${date}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to fetch employee data');
            }

            return data.data?.employees || data.employees || [];
        } catch (error) {
            console.error('Employee map fetch error:', error);
            return [];
        }
    }

    // Render employee availability map as interactive filter
    renderEmployeeMap(employees) {

        let html = `
            <div class="employee-map-container">
                <div class="employee-map-title">${t('select_staff_member', 'Επιλέξτε Μέλος Προσωπικού')}</div>
                <div class="employee-map-grid">
        `;

        // Add "Auto Assign" option as first item (pre-selected)
        html += `
            <div class="employee-map-item auto-assign selected" onclick="BookingSystem.instance.selectEmployeeFilter('auto')" title="${t('auto_assign_tooltip', 'Αφήστε μας να αναθέσουμε το καλύτερο διαθέσιμο μέλος προσωπικού')}">
                <div class="employee-avatar-small auto-assign-avatar">
                    <i class="fas fa-magic"></i>
                </div>
                <div class="employee-name-small">${t('auto_assign', 'Αυτόματη Ανάθεση')}</div>
            </div>
        `;

        // Add individual employees
        if (employees && employees.length > 0) {
            employees.forEach(employee => {
                const avatarStyle = `background-color: ${employee.color || '#3498db'}`;
                const initial = employee.name ? employee.name.charAt(0).toUpperCase() : '?';
                const isUnavailable = !employee.hasAvailableSlots;
                const unavailableClass = isUnavailable ? ' unavailable' : '';

                html += `
                    <div class="employee-map-item${unavailableClass}" onclick="BookingSystem.instance.selectEmployeeFilter('${employee.id}')" title="${employee.name}${isUnavailable ? ' - No available slots' : ''}">
                        <div class="employee-avatar-small" style="${avatarStyle}">${initial}</div>
                        <div class="employee-name-small">${employee.name}</div>
                    </div>
                `;
            });
        }

        html += `
                </div>
            </div>
        `;

        return html;
    }

    async getCategoryData(categoryId) {
        const cacheKey = `category_${categoryId}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        // Category IDs are strings, not numbers - compare directly

        // Try to get from categories cache
        if (this.cache.has('categories')) {
            const categories = this.cache.get('categories');
            const category = categories.find(c => c.id === categoryId);
            if (category) {
                this.cache.set(cacheKey, category);
                return category;
            }
        }

        throw new Error('Category data not found');
    }

    // Calendar methods
    async generateCalendar(date, availability) {
        const year = date.getFullYear();
        const month = date.getMonth();
        const today = new Date();
        
        // Update month display
        setTimeout(() => {
            const monthNames = [
                t('january', 'January'),
                t('february', 'February'),
                t('march', 'March'),
                t('april', 'April'),
                t('may', 'May'),
                t('june', 'June'),
                t('july', 'July'),
                t('august', 'August'),
                t('september', 'September'),
                t('october', 'October'),
                t('november', 'November'),
                t('december', 'December')
            ];
            const monthElement = document.getElementById('calendarMonth');
            if (monthElement) {
                monthElement.textContent = `${monthNames[month]} ${year}`;
            }
        }, 100);

        // Days of the week (Monday first)
        const daysOfWeek = [
            t('mon', 'Mon'),
            t('tue', 'Tue'),
            t('wed', 'Wed'),
            t('thu', 'Thu'),
            t('fri', 'Fri'),
            t('sat', 'Sat'),
            t('sun', 'Sun')
        ];
        let html = '';

        daysOfWeek.forEach(day => {
            html += `<div class="calendar-day-header">${day}</div>`;
        });

        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        let startingDayOfWeek = firstDay.getDay();

        // Convert Sunday (0) to be last day (6) for Monday-first calendar
        startingDayOfWeek = startingDayOfWeek === 0 ? 6 : startingDayOfWeek - 1;

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < startingDayOfWeek; i++) {
            html += '<div class="calendar-day"></div>';
        }

        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const currentDate = new Date(year, month, day);
            const dateStr = this.formatDateForAPI(currentDate);
            const isToday = currentDate.toDateString() === today.toDateString();
            const isPast = currentDate < today;
            const isAvailable = availability[dateStr] === true;
            const isTooFarInFuture = await this.isBeyondBookingLimit(currentDate);

            // Check for special days
            const specialDayInfo = await this.getSpecialDayInfo(dateStr);

            let classes = 'calendar-day';
            let dayContent = day;

            if (isToday) classes += ' today';

            if (isPast) {
                classes += ' disabled past';
            } else if (isTooFarInFuture) {
                classes += ' disabled future-limit';
                dayContent += '<div class="future-limit-indicator">Not yet available</div>';
            } else if (specialDayInfo && specialDayInfo.isClosed) {
                classes += ' disabled special-day-closed';
                dayContent += '<div class="special-day-indicator">Closed</div>';
                if (specialDayInfo.note) {
                    dayContent += `<div class="special-day-note">${specialDayInfo.note}</div>`;
                }
            } else if (specialDayInfo && specialDayInfo.hasCustomHours) {
                classes += ' special-day-custom';
                dayContent += '<div class="special-day-indicator">Special Hours</div>';
                if (specialDayInfo.note) {
                    dayContent += `<div class="special-day-note">${specialDayInfo.note}</div>`;
                }
            } else if (!isAvailable) {
                classes += ' disabled';
            }

            // Make closed special days non-clickable
            const isClosedSpecialDay = specialDayInfo && specialDayInfo.isClosed;
            const onclick = (isPast || !isAvailable || isTooFarInFuture || isClosedSpecialDay) ? '' : `onclick="BookingSystem.instance.selectDate('${dateStr}')"`;

            html += `<div class="${classes}" data-date="${dateStr}" ${onclick}>${dayContent}</div>`;
        }

        return html;
    }

    // Render regular time slots for auto employee selection
    renderAutoTimeSlots(timeSlots) {
        let html = '';

        Object.entries(timeSlots).forEach(([timeStr, slotData]) => {
            // Handle interval breaks
            if (slotData.is_interval_break) {
                html += `
                    <div class="interval-break">
                        <div class="interval-break-line"></div>
                        <div class="interval-break-text">Break ${slotData.break_start} - ${slotData.break_end}</div>
                        <div class="interval-break-line"></div>
                    </div>
                `;
                return;
            }

            const isAvailable = slotData.available;
            const reason = slotData.reason;

            let className = 'time-slot';
            let slotContent = timeStr;
            let onclick = '';

            if (!isAvailable) {
                className += ' disabled';

                // Add specific styling based on reason
                if (reason === 'past_time') {
                    className += ' past-time';
                    slotContent += '<div class="deletion-line"></div>';
                } else if (reason === 'booked') {
                    className += ' booked';
                    slotContent += `<div class="booked-indicator">${t('booked', 'Κλεισμένο')}</div>`;
                } else if (reason === 'duration_conflict') {
                    className += ' duration-conflict';
                    slotContent += `<div class="duration-conflict-indicator">${t('duration_conflict', 'Σύγκρουση διάρκειας')}</div>`;
                } else if (reason === 'insufficient_time') {
                    className += ' insufficient-time';
                    slotContent += `<div class="insufficient-time-indicator">${t('insufficient_time', 'Ανεπαρκής χρόνος')}</div>`;
                } else if (reason === 'employee_unavailable') {
                    className += ' employee-unavailable';
                    slotContent += `<div class="employee-unavailable-indicator">${t('staff_unavailable', 'Προσωπικό μη διαθέσιμο')}</div>`;
                }
            } else {
                onclick = `onclick="BookingSystem.instance.selectTime('${timeStr}')"`;
            }

            html += `
                <div class="${className}" ${onclick}>
                    ${slotContent}
                </div>
            `;
        });

        return html;
    }

    // Fast render using cached data with proper label consistency
    renderFilteredTimeSlotsFromCache() {
        if (!this.cachedTimeSlots || !this.employeeTimeSlotCache) {
            return '<div class="error">No cached data available</div>';
        }

        let html = '';
        const selectedEmployee = this.selectedEmployeeFilter || 'auto';
        const selectedEmployeeName = this.getSelectedEmployeeName(selectedEmployee);

        for (const [timeStr, slotData] of Object.entries(this.cachedTimeSlots)) {
            let isAvailable = slotData.available;
            let reason = slotData.reason;
            let className = 'time-slot manual-selection';
            let slotContent = timeStr;
            let onclick = '';

            // If specific employee is selected, check their availability using cached data
            if (selectedEmployee !== 'auto') {
                const availableEmployees = this.employeeTimeSlotCache[timeStr] || [];
                const employeeAvailable = availableEmployees.some(emp => emp.id === selectedEmployee);

                if (!employeeAvailable && isAvailable) {
                    // Slot is generally available but not for selected employee
                    className += ' employee-filtered';
                    isAvailable = false;
                    reason = 'employee_unavailable';
                }
            }

            if (!isAvailable) {
                className += ' disabled';

                // Add specific styling based on reason with consistent labels
                if (reason === 'past_time') {
                    className += ' past-time';
                    slotContent += '<div class="deletion-line"></div>';
                } else if (reason === 'booked') {
                    className += ' booked';
                    if (selectedEmployee === 'auto') {
                        slotContent += '<div class="booked-indicator">Booked</div>';
                    } else {
                        slotContent += `<div class="booked-indicator">${selectedEmployeeName} booked</div>`;
                    }
                } else if (reason === 'duration_conflict') {
                    className += ' duration-conflict';
                    if (selectedEmployee === 'auto') {
                        slotContent += '<div class="duration-conflict-indicator">Duration conflict</div>';
                    } else {
                        slotContent += `<div class="duration-conflict-indicator">${selectedEmployeeName} conflict</div>`;
                    }
                } else if (reason === 'insufficient_time') {
                    className += ' insufficient-time';
                    if (selectedEmployee === 'auto') {
                        slotContent += '<div class="insufficient-time-indicator">Insufficient time</div>';
                    } else {
                        slotContent += `<div class="insufficient-time-indicator">${selectedEmployeeName} insufficient time</div>`;
                    }
                } else if (reason === 'employee_unavailable') {
                    className += ' employee-unavailable';
                    if (selectedEmployee === 'auto') {
                        slotContent += '<div class="employee-unavailable-indicator">Staff unavailable</div>';
                    } else {
                        slotContent += `<div class="employee-unavailable-indicator">${selectedEmployeeName} unavailable</div>`;
                    }
                }
            } else {
                onclick = `onclick="BookingSystem.instance.selectTimeSlot('${timeStr}')"`;
            }

            html += `
                <div class="${className}" data-time="${timeStr}" ${onclick}>
                    ${slotContent}
                </div>
            `;
        }

        return html;
    }

    // Get selected employee name for consistent labeling
    getSelectedEmployeeName(employeeId) {
        if (employeeId === 'auto') return 'Staff';

        if (this.availableEmployees) {
            const employee = this.availableEmployees.find(emp => emp.id === employeeId);
            return employee ? employee.name : 'Staff';
        }

        return 'Staff';
    }

    // Get special day information for calendar display
    async getSpecialDayInfo(dateStr) {
        try {
            // Check if we have cached special days
            if (!this.specialDaysCache) {
                const response = await fetch(`${this.apiBase}/settings`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data.special_days) {
                        this.specialDaysCache = JSON.parse(data.data.special_days);
                    } else {
                        this.specialDaysCache = {};
                    }
                } else {
                    this.specialDaysCache = {};
                }
            }

            const specialDay = this.specialDaysCache[dateStr];
            if (!specialDay) return null;

            return {
                isClosed: !specialDay.hours || specialDay.hours.length === 0,
                hasCustomHours: specialDay.hours && specialDay.hours.length > 0,
                hours: specialDay.hours || [],
                note: specialDay.note || ''
            };

        } catch (error) {
            console.warn('Error fetching special day info:', error);
            return null;
        }
    }

    // Legacy method for backward compatibility (now calls cached version when possible)
    async renderFilteredTimeSlots(timeSlots, serviceData) {
        // If we have cached data, use the fast method
        if (this.employeeTimeSlotCache && this.cachedTimeSlots) {
            return this.renderFilteredTimeSlotsFromCache();
        }

        // Fallback to original method for initial load
        let html = '';
        const selectedEmployee = this.selectedEmployeeFilter || 'auto';

        for (const [timeStr, slotData] of Object.entries(timeSlots)) {
            let isAvailable = slotData.available;
            let reason = slotData.reason;
            let className = 'time-slot manual-selection';
            let slotContent = timeStr;
            let onclick = '';

            // If specific employee is selected, check their availability
            if (selectedEmployee !== 'auto') {
                // Get available employees for this time slot
                const availableEmployees = await this.getAvailableEmployeesForTimeSlot(
                    serviceData.id,
                    this.bookingData.date,
                    timeStr
                );

                // Check if selected employee is available for this slot
                const employeeAvailable = availableEmployees.some(emp => emp.id === selectedEmployee);

                if (!employeeAvailable && isAvailable) {
                    // Slot is generally available but not for selected employee
                    className += ' employee-filtered';
                    isAvailable = false;
                    reason = 'employee_unavailable';
                }
            }

            if (!isAvailable) {
                className += ' disabled';

                // Add specific styling based on reason
                if (reason === 'past_time') {
                    className += ' past-time';
                    slotContent += '<div class="deletion-line"></div>';
                } else if (reason === 'booked') {
                    className += ' booked';
                    slotContent += '<div class="booked-indicator">Booked</div>';
                } else if (reason === 'duration_conflict') {
                    className += ' duration-conflict';
                    slotContent += '<div class="duration-conflict-indicator">Duration conflict</div>';
                } else if (reason === 'insufficient_time') {
                    className += ' insufficient-time';
                    slotContent += '<div class="insufficient-time-indicator">Insufficient time</div>';
                } else if (reason === 'employee_unavailable') {
                    className += ' employee-unavailable';
                    slotContent += '<div class="employee-unavailable-indicator">Staff unavailable</div>';
                }
            } else {
                onclick = `onclick="BookingSystem.instance.selectTimeSlot('${timeStr}')"`;
            }

            html += `
                <div class="${className}" data-time="${timeStr}" ${onclick}>
                    ${slotContent}
                </div>
            `;
        }

        return html;
    }

    // Legacy method for backward compatibility (now calls filtered version)
    async renderManualTimeSlots(timeSlots, serviceData) {
        // Initialize with auto selection
        this.selectedEmployeeFilter = 'auto';
        return await this.renderFilteredTimeSlots(timeSlots, serviceData);
    }

    // Check if an employee has any available slots for the day (using cached data)
    checkEmployeeHasAvailableSlotsFromCache(employeeId) {
        if (!this.employeeTimeSlotCache) return false;

        for (const [timeStr, availableEmployees] of Object.entries(this.employeeTimeSlotCache)) {
            if (availableEmployees.some(emp => emp.id === employeeId)) {
                return true;
            }
        }
        return false;
    }

    // Legacy method for backward compatibility
    async checkEmployeeHasAvailableSlots(employeeId, serviceId, date, timeSlots) {
        for (const [timeStr, slotData] of Object.entries(timeSlots)) {
            if (slotData.available) {
                // Get available employees for this time slot
                const availableEmployees = await this.getAvailableEmployeesForTimeSlot(
                    serviceId,
                    date,
                    timeStr
                );

                // Check if this employee is available for this slot
                if (availableEmployees.some(emp => emp.id === employeeId)) {
                    return true;
                }
            }
        }
        return false;
    }

    // Get available employees for a specific time slot
    async getAvailableEmployeesForTimeSlot(serviceId, date, time) {
        try {
            const response = await fetch(`${this.apiBase}/employees?service_id=${serviceId}&date=${date}&time=${time}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to fetch available employees');
            }

            return data.data?.employees || data.employees || [];
        } catch (error) {
            console.error('Available employees fetch error:', error);
            return [];
        }
    }

    // Toggle time slot expansion for manual selection
    toggleTimeSlot(timeStr) {
        // Close any other expanded slots first
        document.querySelectorAll('.time-slot.manual-selection.expanded').forEach(slot => {
            if (slot.dataset.time !== timeStr) {
                this.collapseTimeSlot(slot);
            }
        });

        // Toggle the clicked slot
        const slot = document.querySelector(`[data-time="${timeStr}"]`);
        if (slot) {
            if (slot.classList.contains('expanded')) {
                this.collapseTimeSlot(slot);
            } else {
                this.expandTimeSlot(slot);
            }
        }
    }

    // Expand a time slot to show employee options
    expandTimeSlot(slotElement) {
        slotElement.classList.add('expanded');
        const employeeList = slotElement.querySelector('.employee-list');
        const arrow = slotElement.querySelector('.expand-arrow');

        if (employeeList) {
            employeeList.style.display = 'block';
        }
        if (arrow) {
            arrow.textContent = '▲';
        }
    }

    // Collapse a time slot to hide employee options
    collapseTimeSlot(slotElement) {
        slotElement.classList.remove('expanded');
        const employeeList = slotElement.querySelector('.employee-list');
        const arrow = slotElement.querySelector('.expand-arrow');

        if (employeeList) {
            employeeList.style.display = 'none';
        }
        if (arrow) {
            arrow.textContent = '▼';
        }
    }

    // Select employee filter for manual selection services (fast, uses cached data)
    selectEmployeeFilter(employeeId) {

        // Store selected employee filter
        this.selectedEmployeeFilter = employeeId;

        // Update visual selection in employee map
        document.querySelectorAll('.employee-map-item').forEach(item => {
            item.classList.remove('selected');
        });

        if (employeeId === 'auto') {
            document.querySelector('.employee-map-item.auto-assign').classList.add('selected');
        } else {
            const selectedItem = document.querySelector(`[onclick*="${employeeId}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
            }
        }

        // Fast update using cached data
        this.updateTimeSlotDisplayFast();
    }

    // Fast time slot update using cached data (no API calls)
    updateTimeSlotDisplayFast() {
        const timeSlotsGrid = document.querySelector('.time-slots-grid');
        if (!timeSlotsGrid || !this.cachedTimeSlots || !this.cachedServiceData) {
            console.warn('No cached data available for fast filtering');
            return;
        }

        // Show loading spinner briefly
        timeSlotsGrid.style.opacity = '0.7';
        timeSlotsGrid.style.position = 'relative';

        // Add spinner
        const spinner = document.createElement('div');
        spinner.className = 'employee-filter-spinner';
        spinner.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        timeSlotsGrid.appendChild(spinner);

        // Use setTimeout to allow spinner to show, then update (fast)
        setTimeout(() => {
            // Render filtered time slots using cached data
            const filteredHtml = this.renderFilteredTimeSlotsFromCache();

            timeSlotsGrid.innerHTML = filteredHtml;
            timeSlotsGrid.style.opacity = '1';
        }, 100); // Very brief delay to show spinner
    }

    // Select time slot for manual selection (simplified)
    selectTimeSlot(time) {

        this.bookingData.time = time;

        // Set employee based on current filter
        if (this.selectedEmployeeFilter === 'auto') {
            this.bookingData.employee = null; // Will be auto-assigned
        } else {
            this.bookingData.employee = this.selectedEmployeeFilter;
        }

        this.saveBookingData(); // Save after updating time slot

        // Visual feedback
        document.querySelectorAll('.time-slot').forEach(slot => {
            slot.classList.remove('selected');
        });

        const timeSlot = document.querySelector(`[data-time="${time}"]`);
        if (timeSlot) {
            timeSlot.classList.add('selected');
        }

        // Proceed to next step after a short delay
        setTimeout(async () => await this.nextStep(), 300);
    }

    async changeMonth(direction) {

        if (!this.currentCalendarDate) {
            this.currentCalendarDate = new Date();
        }

        // Update the calendar date
        this.currentCalendarDate.setMonth(this.currentCalendarDate.getMonth() + direction);

        // Fetch availability for the new month
        const availability = await this.fetchAvailability(this.bookingData.service, this.currentCalendarDate);

        // Re-render the calendar
        const calendarGrid = document.getElementById('calendarGrid');
        if (calendarGrid) {
            calendarGrid.innerHTML = await this.generateCalendar(this.currentCalendarDate, availability);
        }
    }

    // Verification input handling
    handleVerificationInput(event) {
        const input = event.target;
        const index = parseInt(input.getAttribute('data-index'));
        const value = input.value;

        // Only allow numbers
        if (!/^\d*$/.test(value)) {
            input.value = '';
            return;
        }

        // Clear any previous error
        this.hideVerificationError();

        // Move to next input
        if (value && index < 3) {
            const nextInput = document.querySelector(`[data-index="${index + 1}"]`);
            if (nextInput) {
                nextInput.focus();
            }
        }

        // Auto-validate when all digits are entered
        const allDigits = document.querySelectorAll('.verification-digit');
        const allFilled = Array.from(allDigits).every(digit => digit.value !== '');

        if (allFilled && !this.verificationInProgress) {
            setTimeout(async () => await this.nextStep(), 500);
        }
    }

    // Handle paste events for verification code
    handleVerificationPaste(event) {
        event.preventDefault();

        const pastedData = (event.clipboardData || window.clipboardData).getData('text');
        const digits = pastedData.replace(/\D/g, ''); // Remove non-digits

        if (digits.length === 4) {
            const inputs = document.querySelectorAll('.verification-digit');

            // Fill each input with the corresponding digit
            for (let i = 0; i < 4; i++) {
                if (inputs[i]) {
                    inputs[i].value = digits[i];
                }
            }

            // Focus the last input
            if (inputs[3]) {
                inputs[3].focus();
            }

            // Clear any previous error
            this.hideVerificationError();

            // Auto-validate after paste
            if (!this.verificationInProgress) {
                setTimeout(async () => await this.nextStep(), 500);
            }
        } else if (digits.length > 0) {
            this.showVerificationError('Please paste a 4-digit verification code');
        }
    }

    // Handle keydown for backspace navigation
    handleVerificationKeydown(event) {
        const input = event.target;
        const index = parseInt(input.getAttribute('data-index'));

        // Handle backspace
        if (event.key === 'Backspace' && !input.value && index > 0) {
            const prevInput = document.querySelector(`[data-index="${index - 1}"]`);
            if (prevInput) {
                prevInput.focus();
                prevInput.value = '';
            }
        }
    }

    // Timer methods
    startResendTimer() {
        this.resendTimeout = 60;
        this.updateResendTimer();
        
        this.verificationTimer = setInterval(() => {
            this.resendTimeout--;
            this.updateResendTimer();
            
            if (this.resendTimeout <= 0) {
                clearInterval(this.verificationTimer);
                this.verificationTimer = null;
            }
        }, 1000);
    }

    updateResendTimer() {
        const timerElement = document.getElementById('resendTimer');
        if (timerElement) {
            if (this.resendTimeout > 0) {
                timerElement.textContent = t('resend_timer', 'Μπορείτε να στείλετε ξανά τον κωδικό σε {seconds} δευτερόλεπτα').replace('{seconds}', this.resendTimeout);
            } else {
                timerElement.textContent = '';
            }
        }
    }

    // UI methods
    updateProgressBar() {
        const progressFill = document.getElementById('progressFill');
        const steps = document.querySelectorAll('.progress-step');
        
        if (progressFill) {
            const progress = (this.currentStep / this.totalSteps) * 100;
            progressFill.style.width = `${progress}%`;
        }
        
        steps.forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber === this.currentStep) {
                step.classList.add('active');
            } else if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            }
        });
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (prevBtn) {
            prevBtn.style.display = this.currentStep > 1 ? 'inline-flex' : 'none';
        }

        if (nextBtn) {
            if (this.currentStep === this.totalSteps) {
                nextBtn.style.display = 'none';
            } else {
                nextBtn.style.display = 'inline-flex';
                nextBtn.innerHTML = this.currentStep === 6 ?
                    `<i class="fas fa-check"></i> ${t('verify', 'Verify')}` :
                    `${t('next', 'Next')} <i class="fas fa-arrow-right"></i>`;

                // Enable/disable next button based on current step validation
                const isStepValid = this.isCurrentStepValid();
                nextBtn.disabled = !isStepValid;
                nextBtn.style.opacity = isStepValid ? '1' : '0.5';
                nextBtn.style.cursor = isStepValid ? 'pointer' : 'not-allowed';
            }
        }
    }

    // Setup event listeners for step 5 form fields
    setupStep5EventListeners() {
        // Setup customer type toggle
        const toggleButton = document.getElementById('customerTypeToggle');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                this.toggleCustomerType();
            });
        }

        const nameField = document.getElementById('customerName');
        const emailField = document.getElementById('customerEmail');
        const phoneField = document.getElementById('customerPhone');

        if (nameField) {
            nameField.addEventListener('input', () => {
                this.bookingData.customer.name = nameField.value.trim();
                this.saveBookingData();
                this.updateNavigationButtons();
            });
        }

        if (emailField) {
            emailField.addEventListener('input', () => {
                this.bookingData.customer.email = emailField.value.trim();
                this.saveBookingData();
                this.updateNavigationButtons();

                // If in returning customer mode, trigger lookup
                if (this.bookingData.isReturningCustomer) {
                    this.debouncedCustomerLookup(emailField.value.trim());
                }
            });
        }

        if (phoneField) {
            phoneField.addEventListener('input', () => {
                this.bookingData.customer.phone = phoneField.value.trim();
                this.saveBookingData();
                this.updateNavigationButtons();

                // If in returning customer mode and verification is SMS, trigger lookup
                if (this.bookingData.isReturningCustomer && this.verificationMethod === 'sms') {
                    this.debouncedCustomerLookup(phoneField.value.trim());
                }
            });
        }
    }

    toggleCustomerType() {
        this.bookingData.isReturningCustomer = !this.bookingData.isReturningCustomer;
        this.saveBookingData();

        // Re-render the step
        this.loadStep(5);
    }

    // Debounced customer lookup to avoid too many API calls
    debouncedCustomerLookup(value) {
        clearTimeout(this.lookupTimeout);
        this.lookupTimeout = setTimeout(() => {
            if (value && value.length > 3) {
                this.lookupCustomer(value);
            }
        }, 500);
    }

    async lookupCustomer(email) {
        if (!email || !email.includes('@')) return;

        const statusElement = document.getElementById('customerLookupStatus');
        const loadingElement = document.getElementById('lookupLoading');
        const foundElement = document.getElementById('lookupFound');
        const notFoundElement = document.getElementById('lookupNotFound');

        if (!statusElement) return;

        // Show loading state
        statusElement.style.display = 'block';
        loadingElement.style.display = 'block';
        foundElement.style.display = 'none';
        notFoundElement.style.display = 'none';

        try {
            const response = await fetch(`${this.apiBase}/customer-lookup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            });

            const data = await response.json();

            if (data.success && data.data.found) {
                // Customer found
                const customer = data.data.customer;
                this.bookingData.customer = {
                    ...this.bookingData.customer,
                    id: customer.id,
                    name: customer.name,
                    email: customer.email,
                    phone: customer.phone,
                    language: customer.language
                };
                this.saveBookingData();

                // Show found state
                loadingElement.style.display = 'none';
                foundElement.style.display = 'block';
                document.getElementById('foundCustomerName').textContent = customer.name;

            } else {
                // Customer not found
                loadingElement.style.display = 'none';
                notFoundElement.style.display = 'block';

                // Clear customer data except email
                this.bookingData.customer = {
                    name: '',
                    email: email,
                    phone: '',
                    language: 'el'
                };
                this.saveBookingData();
            }

        } catch (error) {
            console.error('Customer lookup error:', error);
            loadingElement.style.display = 'none';
            notFoundElement.style.display = 'block';
        }
    }

    // Check if current step is valid (for button state)
    isCurrentStepValid() {
        switch (this.currentStep) {
            case 1:
                return this.bookingData.category !== null;
            case 2:
                return this.bookingData.service !== null;
            case 3:
                return this.bookingData.date !== null;
            case 4:
                return this.bookingData.time !== null;
            case 5:
                // Check actual form fields for step 5
                const isReturningCustomer = this.bookingData.isReturningCustomer || false;

                if (isReturningCustomer) {
                    // For returning customers, only validate the verification field
                    if (this.verificationMethod === 'sms') {
                        const phoneField = document.getElementById('customerPhone');
                        if (!phoneField) return false;
                        const phone = phoneField.value.trim();
                        return phone.length >= 10;
                    } else {
                        const emailField = document.getElementById('customerEmail');
                        if (!emailField) return false;
                        const email = emailField.value.trim();
                        return email.includes('@') && email.includes('.');
                    }
                } else {
                    // For new customers, validate all required fields
                    const nameField = document.getElementById('customerName');
                    const emailField = document.getElementById('customerEmail');
                    const phoneField = document.getElementById('customerPhone');

                    if (!nameField || !emailField) return false;

                    const name = nameField.value.trim();
                    const email = emailField.value.trim();
                    const phone = phoneField ? phoneField.value.trim() : '';

                    // Check if phone is required (from store settings)
                    const phoneRequired = this.storeSettings?.require_phone || false;

                    // Basic validation
                    const nameValid = name.length >= 2;
                    const emailValid = email.includes('@') && email.includes('.');
                    const phoneValid = !phoneRequired || phone.length >= 10;

                    return nameValid && emailValid && phoneValid;
                }
            case 6:
                return this.bookingData.verificationCode !== '';
            default:
                return true;
        }
    }

    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'flex';
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        if (errorDiv && errorText) {
            errorText.textContent = message;
            errorDiv.style.display = 'flex';
            
            // Auto-hide after 5 seconds
            setTimeout(() => this.hideError(), 5000);
        }
    }

    hideError() {
        const errorDiv = document.getElementById('errorMessage');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    showSuccess(message) {
        // Create temporary success message
        const successDiv = document.createElement('div');
        successDiv.className = 'error-message';
        successDiv.style.background = '#c6f6d5';
        successDiv.style.color = '#22543d';
        successDiv.style.borderColor = '#9ae6b4';
        successDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;

        document.querySelector('.main-content').insertBefore(successDiv, document.getElementById('stepContent'));

        setTimeout(() => {
            successDiv.remove();
        }, 3000);
    }

    // Verification-specific error handling
    showVerificationError(message) {
        const errorDiv = document.getElementById('verificationError');
        const errorText = document.getElementById('verificationErrorText');

        if (errorDiv && errorText) {
            errorText.textContent = message;
            errorDiv.style.display = 'flex';

            // Add error styling to verification inputs
            const inputs = document.querySelectorAll('.verification-digit');
            inputs.forEach(input => {
                input.classList.add('error');
            });

            // Auto-hide after 5 seconds
            setTimeout(() => this.hideVerificationError(), 5000);
        }
    }

    hideVerificationError() {
        const errorDiv = document.getElementById('verificationError');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }

        // Remove error styling from verification inputs
        const inputs = document.querySelectorAll('.verification-digit');
        inputs.forEach(input => {
            input.classList.remove('error');
        });
    }

    // Setup verification form
    setupVerification() {
        // Focus the first input
        const firstInput = document.querySelector('.verification-digit[data-index="0"]');
        if (firstInput) {
            firstInput.focus();
        }

        // Set the email in the message
        const emailElement = document.getElementById('verificationEmail');
        if (emailElement && this.bookingData.customerEmail) {
            emailElement.textContent = this.bookingData.customerEmail;
        }

        // Clear any existing errors
        this.hideVerificationError();
    }

    showFieldError(errorElementId, inputElementId, message) {
        const errorElement = document.getElementById(errorElementId);
        const inputElement = document.getElementById(inputElementId);
        
        if (errorElement) {
            errorElement.textContent = message;
        }
        
        if (inputElement) {
            inputElement.classList.add('error');
        }
    }

    // Utility methods
    formatDate(dateStr) {
        const date = new Date(dateStr);

        // Get day, month, year
        const day = date.getDate();
        const month = date.getMonth();
        const year = date.getFullYear();
        const weekday = date.getDay();

        // Greek weekday names
        const weekdays = [
            t('sunday', 'Κυριακή'),
            t('monday', 'Δευτέρα'),
            t('tuesday', 'Τρίτη'),
            t('wednesday', 'Τετάρτη'),
            t('thursday', 'Πέμπτη'),
            t('friday', 'Παρασκευή'),
            t('saturday', 'Σάββατο')
        ];

        // Greek month names
        const months = [
            t('january', 'Ιανουάριος'),
            t('february', 'Φεβρουάριος'),
            t('march', 'Μάρτιος'),
            t('april', 'Απρίλιος'),
            t('may', 'Μάιος'),
            t('june', 'Ιούνιος'),
            t('july', 'Ιούλιος'),
            t('august', 'Αύγουστος'),
            t('september', 'Σεπτέμβριος'),
            t('october', 'Οκτώβριος'),
            t('november', 'Νοέμβριος'),
            t('december', 'Δεκέμβριος')
        ];

        return `${weekdays[weekday]}, ${day} ${months[month]} ${year}`;
    }

    formatDateForAPI(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    async isBeyondBookingLimit(date) {
        // Get booking advance days from settings
        let bookingAdvanceDays = 60; // Default fallback

        try {
            if (!this.bookingSettings) {
                // Fetch settings from API
                const response = await fetch(`${this.apiBase}/settings`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                    const data = await response.json();
                    this.bookingSettings = data.data || {};
                }
            }

            bookingAdvanceDays = parseInt(this.bookingSettings.booking_advance_days) || 60;
        } catch (error) {
            console.warn('Could not fetch booking settings, using default:', error);
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0); // Reset time to start of day

        const maxBookingDate = new Date(today);
        maxBookingDate.setDate(today.getDate() + bookingAdvanceDays);

        const checkDate = new Date(date);
        checkDate.setHours(0, 0, 0, 0); // Reset time to start of day

        return checkDate > maxBookingDate;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    isValidPhone(phone) {
        const phoneRegex = /^(69|21|22|23|24|25|26|27|28)\d{8}$/;
        const cleanPhone = phone.replace(/[^\d]/g, '');
        return phoneRegex.test(cleanPhone);
    }

    // Reset methods
    newBooking() {
        this.bookingData = {
            category: null,
            service: null,
            date: null,
            time: null,
            employee: null,
            customer: {
                name: '',
                email: '',
                phone: ''
            },
            verificationCode: '',
            bookingId: null
        };

        // Clear saved booking data
        this.clearBookingData();

        this.currentStep = 1;
        this.cache.clear();
        this.loadStep(1);
    }
}

// Global functions for onclick handlers
window.hideError = function() {
    BookingSystem.instance.hideError();
};

window.nextStep = async function() {
    await BookingSystem.instance.nextStep();
};

window.previousStep = function() {
    BookingSystem.instance.previousStep();
};

// Initialize global instance
BookingSystem.instance = new BookingSystem();
