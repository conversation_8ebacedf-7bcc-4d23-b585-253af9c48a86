<?php

class EmailTemplate
{
    private static function getBaseTemplate(): string
    {
        return '
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}}</title>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        /* Header */
        .email-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .email-header h1 {
            font-size: 1.875rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .email-header .subtitle {
            font-size: 1.125rem;
            color: #718096;
            margin-bottom: 0;
        }
        
        /* Body */
        .email-body {
            padding: 40px 30px;
            background: white;
        }
        
        .email-body h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .email-body p {
            color: #4a5568;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        /* Verification Code Box */
        .verification-code-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .verification-code-box {
            display: inline-block;
            background: #f7fafc;
            border: 2px dashed #a0aec0;
            border-radius: 12px;
            padding: 25px 40px;
            margin: 20px 0;
        }
        
        .verification-code {
            font-family: "Courier New", monospace;
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            letter-spacing: 8px;
            margin: 0;
        }
        
        .verification-label {
            font-size: 0.875rem;
            color: #718096;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* Booking Summary */
        .booking-summary {
            background: #f7fafc;
            border-radius: 16px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .summary-item:last-child {
            border-bottom: none;
        }
        
        .summary-label {
            font-weight: 600;
            color: #2d3748;
        }
        
        .summary-value {
            color: #4a5568;
            font-weight: 500;
        }
        
        .summary-price {
            font-weight: 700;
            color: #667eea;
            font-size: 1.125rem;
        }
        
        /* Booking ID */
        .booking-id-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .booking-id {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            font-family: "Courier New", monospace;
            font-size: 1.125rem;
            font-weight: 600;
            letter-spacing: 2px;
        }
        
        /* Buttons */
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            text-align: center;
            margin: 10px 5px;
            transition: transform 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        /* Footer */
        .email-footer {
            background: #f7fafc;
            padding: 30px;
            text-align: center;
            color: #718096;
            font-size: 0.875rem;
        }
        
        .email-footer p {
            margin-bottom: 10px;
        }
        
        /* Responsive */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .email-header,
            .email-body,
            .email-footer {
                padding: 20px;
            }
            
            .verification-code {
                font-size: 1.5rem;
                letter-spacing: 4px;
            }
            
            .verification-code-box {
                padding: 20px 25px;
            }
            
            .booking-summary {
                padding: 20px;
            }
            
            .summary-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1>{{BUSINESS_NAME}}</h1>
            <p class="subtitle">{{HEADER_SUBTITLE}}</p>
        </div>
        
        <div class="email-body">
            {{CONTENT}}
        </div>
        
        <div class="email-footer">
            <p>{{BUSINESS_NAME}}</p>
            <p>This email was sent automatically. Please do not reply to this email.</p>
            <p>If you have any questions, please contact us directly.</p>
        </div>
    </div>
</body>
</html>';
    }

    public static function generateVerificationEmail(string $code, string $businessName = 'Booking System', string $language = 'el'): array
    {
        // Try to get template from database first
        $dbTemplate = self::getEmailTemplateFromDatabase('verification', $language);

        if ($dbTemplate) {
            $subject = str_replace('{{BUSINESS_NAME}}', $businessName, $dbTemplate['subject']);
            $content = str_replace(['{{CODE}}', '{{BUSINESS_NAME}}'], [$code, $businessName], $dbTemplate['content']);

            $body = str_replace([
                '{{TITLE}}',
                '{{BUSINESS_NAME}}',
                '{{HEADER_SUBTITLE}}',
                '{{CONTENT}}'
            ], [
                $subject,
                $businessName,
                'Email Verification',
                $content
            ], self::getBaseTemplate());

            return ['subject' => $subject, 'body' => $body];
        }

        // Fallback to hardcoded templates
        $templates = self::getVerificationEmailTemplates();
        $template = $templates[$language] ?? $templates['el']; // Fallback to Greek

        $content = str_replace('{{CODE}}', $code, $template['content']);
        $subject = $template['title'] . ' - ' . $businessName;

        $body = str_replace([
            '{{TITLE}}',
            '{{BUSINESS_NAME}}',
            '{{HEADER_SUBTITLE}}',
            '{{CONTENT}}'
        ], [
            $subject,
            $businessName,
            $template['subtitle'],
            $content
        ], self::getBaseTemplate());

        return ['subject' => $subject, 'body' => $body];
    }

    private static function getEmailTemplateFromDatabase(string $templateType, string $language): ?array
    {
        try {
            require_once __DIR__ . '/tenant_manager.php';
            $db = TenantManager::getDatabase();

            $template = $db->fetchRow(
                "SELECT * FROM email_templates WHERE template_type = :type AND language = :lang",
                [':type' => $templateType, ':lang' => $language]
            );

            // Check if template is enabled
            if ($template && isset($template['is_enabled']) && $template['is_enabled'] == 0) {
                error_log("Email template '{$templateType}' for language '{$language}' is disabled");
                return null;
            }

            return $template;
        } catch (Exception $e) {
            error_log("Failed to get email template from database: " . $e->getMessage());
            return null;
        }
    }

    private static function getVerificationEmailTemplates(): array
    {
        return [
            'el' => [
                'title' => 'Επιβεβαίωση Email',
                'subtitle' => 'Επιβεβαιώστε το email σας για να ολοκληρώσετε την κράτηση',
                'content' => '
                    <h2>Απαιτείται Επιβεβαίωση Email</h2>
                    <p>Σας ευχαριστούμε για την αίτηση κράτησης. Για να ολοκληρώσετε την κράτησή σας, παρακαλώ επιβεβαιώστε τη διεύθυνση email σας χρησιμοποιώντας τον παρακάτω κωδικό:</p>

                    <div class="verification-code-container">
                        <div class="verification-label">Ο Κωδικός Επιβεβαίωσής σας</div>
                        <div class="verification-code-box">
                            <div class="verification-code">{{CODE}}</div>
                        </div>
                    </div>

                    <p style="text-align: center; color: #e53e3e; font-weight: 600;">Αυτός ο κωδικός θα λήξει σε 5 λεπτά.</p>
                    <p style="text-align: center;">Εισάγετε αυτόν τον κωδικό στη σελίδα κράτησης για να επιβεβαιώσετε την κράτησή σας.</p>
                    <p style="text-align: center; font-size: 0.875rem; color: #718096;">Εάν δεν ζητήσατε αυτόν τον κωδικό, παρακαλώ αγνοήστε αυτό το email.</p>
                '
            ],
            'en' => [
                'title' => 'Email Verification',
                'subtitle' => 'Verify your email to complete your booking',
                'content' => '
                    <h2>Email Verification Required</h2>
                    <p>Thank you for your booking request. To complete your reservation, please verify your email address using the code below:</p>

                    <div class="verification-code-container">
                        <div class="verification-label">Your Verification Code</div>
                        <div class="verification-code-box">
                            <div class="verification-code">{{CODE}}</div>
                        </div>
                    </div>

                    <p style="text-align: center; color: #e53e3e; font-weight: 600;">This code will expire in 5 minutes.</p>
                    <p style="text-align: center;">Enter this code on the booking page to confirm your reservation.</p>
                    <p style="text-align: center; font-size: 0.875rem; color: #718096;">If you did not request this code, please ignore this email.</p>
                '
            ]
        ];
    }

    public static function generateBookingConfirmation(array $bookingData, string $language = 'el'): array
    {
        $businessName = $bookingData['business_name'] ?? 'Booking System';
        $customerName = $bookingData['customer_name'] ?? 'Valued Customer';

        // Try to get template from database first
        $dbTemplate = self::getEmailTemplateFromDatabase('booking_confirmation', $language);

        if ($dbTemplate) {
            $subject = str_replace('{{BUSINESS_NAME}}', $businessName, $dbTemplate['subject']);
            $content = str_replace([
                '{{BUSINESS_NAME}}',
                '{{CUSTOMER_NAME}}',
                '{{SERVICE_NAME}}',
                '{{DATE}}',
                '{{TIME}}',
                '{{DURATION}}',
                '{{EMPLOYEE_NAME}}',
                '{{PRICE}}'
            ], [
                $businessName,
                htmlspecialchars($customerName),
                htmlspecialchars($bookingData['service_name'] ?? ''),
                htmlspecialchars($bookingData['date'] ?? ''),
                htmlspecialchars($bookingData['time'] ?? ''),
                htmlspecialchars($bookingData['duration'] ?? ''),
                htmlspecialchars($bookingData['employee_name'] ?? ''),
                htmlspecialchars($bookingData['price'] ?? '0')
            ], $dbTemplate['content']);

            $body = str_replace([
                '{{TITLE}}',
                '{{BUSINESS_NAME}}',
                '{{HEADER_SUBTITLE}}',
                '{{CONTENT}}'
            ], [
                $subject,
                $businessName,
                'Booking Confirmation',
                $content
            ], self::getBaseTemplate());

            return ['subject' => $subject, 'body' => $body];
        }

        // Fallback to hardcoded templates
        $templates = self::getBookingConfirmationTemplates();
        $template = $templates[$language] ?? $templates['el']; // Fallback to Greek

        $content = str_replace([
            '{{CUSTOMER_NAME}}',
            '{{SERVICE_NAME}}',
            '{{DATE}}',
            '{{TIME}}',
            '{{DURATION}}',
            '{{EMPLOYEE_NAME}}',
            '{{PRICE}}'
        ], [
            htmlspecialchars($customerName),
            htmlspecialchars($bookingData['service_name'] ?? ''),
            htmlspecialchars($bookingData['date'] ?? ''),
            htmlspecialchars($bookingData['time'] ?? ''),
            htmlspecialchars($bookingData['duration'] ?? ''),
            htmlspecialchars($bookingData['employee_name'] ?? ''),
            htmlspecialchars($bookingData['price'] ?? '0')
        ], $template['content']);

        $subject = $template['title'] . ' - ' . $businessName;

        $body = str_replace([
            '{{TITLE}}',
            '{{BUSINESS_NAME}}',
            '{{HEADER_SUBTITLE}}',
            '{{CONTENT}}'
        ], [
            $subject,
            $businessName,
            $template['subtitle'],
            $content
        ], self::getBaseTemplate());

        return ['subject' => $subject, 'body' => $body];
    }

    private static function getBookingConfirmationTemplates(): array
    {
        return [
            'el' => [
                'title' => 'Επιβεβαίωση Κράτησης',
                'subtitle' => 'Το ραντεβού σας επιβεβαιώθηκε',
                'content' => '
                    <h2>🎉 Η Κράτησή σας Επιβεβαιώθηκε!</h2>
                    <p>Αγαπητέ/ή {{CUSTOMER_NAME}},</p>
                    <p>Εξαιρετικά νέα! Η κράτησή σας επιβεβαιώθηκε. Εδώ είναι οι λεπτομέρειες:</p>

                    <div class="booking-summary">
                        <div class="summary-item">
                            <span class="summary-label">Υπηρεσία</span>
                            <span class="summary-value">{{SERVICE_NAME}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Ημερομηνία</span>
                            <span class="summary-value">{{DATE}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Ώρα</span>
                            <span class="summary-value">{{TIME}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Διάρκεια</span>
                            <span class="summary-value">{{DURATION}} λεπτά</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Προσωπικό</span>
                            <span class="summary-value">{{EMPLOYEE_NAME}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Τιμή</span>
                            <span class="summary-price">€{{PRICE}}</span>
                        </div>
                    </div>

                    <p style="text-align: center;">Ανυπομονούμε να σας δούμε!</p>
                    <p style="text-align: center; font-size: 0.875rem; color: #718096;">Παρακαλώ φτάστε 5 λεπτά πριν την ώρα του ραντεβού σας.</p>
                '
            ],
            'en' => [
                'title' => 'Booking Confirmation',
                'subtitle' => 'Your appointment is confirmed',
                'content' => '
                    <h2>🎉 Booking Confirmed!</h2>
                    <p>Dear {{CUSTOMER_NAME}},</p>
                    <p>Great news! Your booking has been confirmed. Here are the details:</p>

                    <div class="booking-summary">
                        <div class="summary-item">
                            <span class="summary-label">Service</span>
                            <span class="summary-value">{{SERVICE_NAME}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Date</span>
                            <span class="summary-value">{{DATE}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Time</span>
                            <span class="summary-value">{{TIME}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Duration</span>
                            <span class="summary-value">{{DURATION}} minutes</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Staff Member</span>
                            <span class="summary-value">{{EMPLOYEE_NAME}}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Price</span>
                            <span class="summary-price">€{{PRICE}}</span>
                        </div>
                    </div>

                    <p style="text-align: center;">We look forward to seeing you!</p>
                    <p style="text-align: center; font-size: 0.875rem; color: #718096;">Please arrive 5 minutes before your appointment time.</p>
                '
            ]
        ];
    }

    /**
     * Generate custom email with consistent template structure
     */
    public static function generateCustomEmail(string $subject, string $content, string $businessName = 'Booking System', string $customerName = '', string $language = 'el'): array
    {
        // Ensure subject has business name prefix if not already present
        if (strpos($subject, $businessName) === false) {
            $subject = $businessName . ' | ' . $subject;
        }

        // Get language-specific subtitle
        $subtitle = $language === 'el' ? 'Έχετε ένα μήνυμα' : 'You have a message';

        // Wrap content with consistent structure
        $wrappedContent = '
            <h2>' . htmlspecialchars($customerName ? ($language === 'el' ? 'Αγαπητέ/ή ' . $customerName : 'Dear ' . $customerName) : ($language === 'el' ? 'Αγαπητέ/ή πελάτη' : 'Dear Customer')) . ',</h2>
            <div class="custom-message-content">
                ' . $content . '
            </div>
            <p style="text-align: center; margin-top: 30px; font-size: 0.875rem; color: #718096;">' .
            ($language === 'el' ? 'Αν έχετε ερωτήσεις, μη διστάσετε να επικοινωνήσετε μαζί μας.' : 'If you have any questions, please don\'t hesitate to contact us.') .
            '</p>
        ';

        $body = str_replace([
            '{{TITLE}}',
            '{{BUSINESS_NAME}}',
            '{{HEADER_SUBTITLE}}',
            '{{CONTENT}}'
        ], [
            $subject,
            $businessName,
            $subtitle,
            $wrappedContent
        ], self::getBaseTemplate());

        return ['subject' => $subject, 'body' => $body];
    }
}
