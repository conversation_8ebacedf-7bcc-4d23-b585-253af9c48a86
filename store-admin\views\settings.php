<?php

/**
 * ******** View
 * Business configuration and ********
 */

$currentTab = $_GET['tab'] ?? 'general';

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    require_once __DIR__ . '/../controllers/********.php';

    try {
        $result = handle********Form($_POST, $db);

        if ($result['success']) {
            $message = $result['message'] ?? '******** saved successfully';
            $messageType = 'success';

            // Force reload ******** after save
            header('Location: ?page=********&tab=' . ($_GET['tab'] ?? 'general') . '&saved=1');
            exit;
        } else {
            $message = $result['error'] ?? 'Failed to save ********';
            $messageType = 'error';
        }
    } catch (Exception $e) {
        $message = 'Error saving ********: ' . $e->getMessage();
        $messageType = 'error';
        error_log("******** save error: " . $e->getMessage());
    }
}

// Show success message if redirected after save
if (isset($_GET['saved'])) {
    $message = '******** saved successfully';
    $messageType = 'success';
}

// Get current ********
$******** = [
    'business_name' => Application::getSetting('business_name', ''),
    'business_email' => Application::getSetting('business_email', ''),
    'business_phone' => Application::getSetting('business_phone', ''),
    'business_address' => Application::getSetting('business_address', ''),
    'booking_advance_days' => Application::getSetting('booking_advance_days', 60),
    'global_buffer_time' => Application::getSetting('global_buffer_time', 15),

    'require_phone' => Application::getSetting('require_phone', '0'),
    'default_language' => Application::getSetting('default_language', 'el'),
    'verification_method' => Application::getSetting('verification_method', 'email'),


    'smtp_enabled' => Application::getSetting('smtp_enabled', '0'),
    'smtp_host' => Application::getSetting('smtp_host', ''),
    'smtp_port' => Application::getSetting('smtp_port', 587),
    'smtp_security' => Application::getSetting('smtp_security', 'tls'),
    'smtp_username' => Application::getSetting('smtp_username', ''),
    'smtp_password' => Application::getSetting('smtp_password', ''),

    // SMS ********
    'sms_enabled' => Application::getSetting('sms_enabled', '0'),
    'sms_provider' => Application::getSetting('sms_provider', 'none'),
    'default_country_code' => Application::getSetting('default_country_code', '+30'),


    // Twilio ********
    'twilio_account_sid' => Application::getSetting('twilio_account_sid', ''),
    'twilio_auth_token' => Application::getSetting('twilio_auth_token', ''),
    'twilio_from_number' => Application::getSetting('twilio_from_number', ''),


    // Nexmo ********
    'nexmo_api_key' => Application::getSetting('nexmo_api_key', ''),
    'nexmo_api_secret' => Application::getSetting('nexmo_api_secret', ''),
    'nexmo_from_number' => Application::getSetting('nexmo_from_number', ''),

    // Apifon ******** (Greek SMS Provider)
    'apifon_api_key' => Application::getSetting('apifon_api_key', ''),
    'apifon_api_token' => Application::getSetting('apifon_api_token', ''),
    'apifon_client_id' => Application::getSetting('apifon_client_id', ''),
    'apifon_username' => Application::getSetting('apifon_username', ''),
    'apifon_sender' => Application::getSetting('apifon_sender', ''),

    // SMStools ******** (Greek SMS Provider)
    'smstools_username' => Application::getSetting('smstools_username', ''),
    'smstools_password' => Application::getSetting('smstools_password', ''),
    'smstools_sender' => Application::getSetting('smstools_sender', ''),

    // SMS.to ******** (Global provider)
    'smsto_api_key' => Application::getSetting('smsto_api_key', ''),
    'smsto_sender' => Application::getSetting('smsto_sender', ''),

    // Custom SMS ********
    'custom_sms_webhook' => Application::getSetting('custom_sms_webhook', ''),
    'custom_sms_api_key' => Application::getSetting('custom_sms_api_key', ''),

    'admin_username' => Application::getSetting('admin_username', 'admin'),
];

// Get business hours from ******** (supports multiple periods) with fallback to working_hours table
$businessHours = [];
$days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

// Try to get from new JSON format first
$businessHoursJson = Application::getSetting('business_hours', '');
if ($businessHoursJson) {
    $businessHoursData = json_decode($businessHoursJson, true);
    if ($businessHoursData) {
        foreach ($days as $day) {
            $periods = $businessHoursData[$day] ?? [];
            $businessHours[$day] = [
                'is_open' => !empty($periods),
                'periods' => $periods
            ];
        }
    }
} else {
    // Fallback to old working_hours table format
    foreach ($days as $index => $day) {
        $dayOfWeek = $index + 1; // Monday = 1, Sunday = 7
        $dayData = $db->fetchRow("SELECT * FROM working_hours WHERE day_of_week = :day", [':day' => $dayOfWeek]);
        if ($dayData && $dayData['is_active']) {
            // Check if it has multiple periods stored
            $periods = [];
            if (!empty($dayData['periods'])) {
                $periodsData = json_decode($dayData['periods'], true);
                if ($periodsData) {
                    $periods = $periodsData;
                } else {
                    $periods = [['start' => $dayData['start_time'], 'end' => $dayData['end_time']]];
                }
            } else {
                $periods = [['start' => $dayData['start_time'], 'end' => $dayData['end_time']]];
            }

            $businessHours[$day] = [
                'is_open' => 1,
                'periods' => $periods
            ];
        } else {
            $businessHours[$day] = [
                'is_open' => 0,
                'periods' => []
            ];
        }
    }
}

// Get special days
$specialDaysData = Application::getSetting('special_days', '');
$specialDays = $specialDaysData ? json_decode($specialDaysData, true) : [];
?>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?>">
        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<!-- ******** Tabs -->
<div class="********-tabs">
    <a href="?page=********&tab=general" class="tab-link <?php echo $currentTab === 'general' ? 'active' : ''; ?>">
        <i class="fas fa-cog"></i> <?php echo at('general', 'General'); ?>
    </a>
    <a href="?page=********&tab=services" class="tab-link <?php echo $currentTab === 'services' ? 'active' : ''; ?>">
        <i class="fas fa-concierge-bell"></i> <?php echo at('services', 'Services'); ?>
    </a>
    <a href="?page=********&tab=business_hours" class="tab-link <?php echo $currentTab === 'business_hours' ? 'active' : ''; ?>">
        <i class="fas fa-clock"></i> <?php echo at('business_hours', 'Business Hours'); ?>
    </a>
    <a href="?page=********&tab=special_days" class="tab-link <?php echo $currentTab === 'special_days' ? 'active' : ''; ?>">
        <i class="fas fa-calendar-day"></i> <?php echo at('special_days', 'Special Days'); ?>
    </a>
    <a href="?page=********&tab=email" class="tab-link <?php echo $currentTab === 'email' ? 'active' : ''; ?>">
        <i class="fas fa-envelope"></i> <?php echo at('email_sms', 'Email & SMS'); ?>
    </a>
    <a href="?page=********&tab=admin" class="tab-link <?php echo $currentTab === 'admin' ? 'active' : ''; ?>">
        <i class="fas fa-user-shield"></i> <?php echo at('admin', 'Admin'); ?>
    </a>
</div>

<!-- Tab Content -->
<div class="tab-content">
    <?php if ($currentTab === 'general'): ?>
        <!-- General ******** -->
        <div class="form-container">
            <h3><?php echo at('general_business_********', 'General Business ********'); ?></h3>
            <form method="POST">
                <input type="hidden" name="action" value="save_general">
                <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

                <div class="form-row">
                    <div class="form-group">
                        <label for="business_name"><?php echo at('business_name', 'Business Name'); ?></label>
                        <input type="text" id="business_name" name="business_name"
                            value="<?php echo htmlspecialchars($********['business_name']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="business_email"><?php echo at('business_email', 'Business Email'); ?></label>
                        <input type="email" id="business_email" name="business_email"
                            value="<?php echo htmlspecialchars($********['business_email']); ?>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="business_phone"><?php echo at('business_phone', 'Business Phone'); ?></label>
                        <input type="tel" id="business_phone" name="business_phone"
                            value="<?php echo htmlspecialchars($********['business_phone']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="default_language"><?php echo at('default_language', 'Default Language'); ?></label>
                        <select id="default_language" name="default_language">
                            <option value="el" <?php echo $********['default_language'] === 'el' ? 'selected' : ''; ?>><?php echo at('greek', 'Greek'); ?></option>
                            <option value="en" <?php echo $********['default_language'] === 'en' ? 'selected' : ''; ?>><?php echo at('english', 'English'); ?></option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="business_address"><?php echo at('business_address', 'Business Address'); ?></label>
                    <textarea id="business_address" name="business_address" rows="3"><?php echo htmlspecialchars($********['business_address']); ?></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="booking_advance_days"><?php echo at('booking_advance_days', 'Booking Advance Days'); ?></label>
                        <input type="number" id="booking_advance_days" name="booking_advance_days"
                            value="<?php echo $********['booking_advance_days']; ?>" min="1" max="365">
                        <small><?php echo at('booking_advance_days_help', 'How many days in advance customers can book'); ?></small>
                    </div>
                    <div class="form-group">
                        <label for="global_buffer_time"><?php echo at('global_buffer_time', 'Global Buffer Time (minutes)'); ?></label>
                        <input type="number" id="global_buffer_time" name="global_buffer_time"
                            value="<?php echo $********['global_buffer_time']; ?>" min="0" max="60">
                        <small><?php echo at('global_buffer_time_help', 'Minimum time between all appointments (applies to all services)'); ?></small>
                    </div>
                </div>

                <div class="form-group">
                    <div class="toggle-group">
                        <div class="toggle-item">
                            <div class="toggle-label">
                                <div class="toggle-label-text"><?php echo at('require_phone_number', 'Require phone number for bookings'); ?></div>
                                <div class="toggle-label-description"><?php echo at('require_phone_number_help', 'Make phone number a mandatory field during booking process'); ?></div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" name="require_phone"
                                    <?php echo $********['require_phone'] ? 'checked' : ''; ?>>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="verification_method"><?php echo at('booking_verification_method', 'Booking Verification Method'); ?></label>
                    <select id="verification_method" name="verification_method" onchange="toggleVerificationOptions()">
                        <option value="none" <?php echo $********['verification_method'] === 'none' ? 'selected' : ''; ?>>
                            <?php echo at('no_verification', 'No Verification (Skip verification step)'); ?>
                        </option>
                        <option value="email" <?php echo $********['verification_method'] === 'email' ? 'selected' : ''; ?>>
                            <?php echo at('email_verification', 'Email Verification'); ?>
                        </option>
                        <option value="sms" <?php echo $********['verification_method'] === 'sms' ? 'selected' : ''; ?>
                            id="sms-verification-option">
                            <?php echo at('sms_verification', 'SMS Verification'); ?>
                        </option>
                    </select>
                    <small class="form-text text-muted">
                        <?php echo at('verification_method_help', 'Choose how customers verify their booking. SMS option requires SMS messaging to be enabled.'); ?>
                    </small>
                    <div id="sms-verification-warning" class="alert alert-warning" style="display: none; margin-top: 10px;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo at('enable_sms_first', 'Please enable SMS messaging first in the Communications tab to use SMS verification.'); ?>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary"><?php echo at('save_general_********', 'Save General ********'); ?></button>
            </form>
        </div>

    <?php elseif ($currentTab === 'services'): ?>
        <!-- Service ******** -->
        <div class="form-container">
            <h3>Default Service ********</h3>
            <form method="POST">
                <input type="hidden" name="action" value="save_services">
                <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

                <div class="form-row">

                </div>



                <button type="submit" class="btn btn-primary">Save Service ********</button>
            </form>
        </div>

    <?php elseif ($currentTab === 'business_hours'): ?>
        <!-- Business Hours -->
        <div class="form-container">
            <h3>Business Hours</h3>
            <form method="POST">
                <input type="hidden" name="action" value="save_business_hours">
                <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

                <div class="working-hours-grid">
                    <?php foreach ($days as $day): ?>
                        <div class="day-hours">
                            <div class="day-header">
                                <div class="toggle-item" style="border-bottom: none; padding: 8px 0;">
                                    <div class="toggle-label">
                                        <div class="toggle-label-text"><?php echo ucfirst($day); ?></div>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="<?php echo $day; ?>_enabled"
                                            <?php echo $businessHours[$day]['is_open'] ? 'checked' : ''; ?>
                                            onchange="toggleDayHours('<?php echo $day; ?>')">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="day-times" id="<?php echo $day; ?>_times"
                                style="<?php echo $businessHours[$day]['is_open'] ? '' : 'display: none;'; ?>">
                                <div class="periods-container" id="<?php echo $day; ?>_periods">
                                    <?php
                                    $periods = $businessHours[$day]['periods'];
                                    if (empty($periods)) {
                                        $periods = [['start' => '09:00', 'end' => '17:00']];
                                    }
                                    foreach ($periods as $index => $period):
                                    ?>
                                        <div class="time-period" data-period="<?php echo $index; ?>">
                                            <input type="time" name="<?php echo $day; ?>_start[]"
                                                value="<?php echo $period['start'] ?? '09:00'; ?>" required>
                                            <span>to</span>
                                            <input type="time" name="<?php echo $day; ?>_end[]"
                                                value="<?php echo $period['end'] ?? '17:00'; ?>" required>
                                            <?php if ($index > 0): ?>
                                                <button type="button" class="btn-remove-period" onclick="removePeriod('<?php echo $day; ?>', <?php echo $index; ?>)">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="addPeriod('<?php echo $day; ?>')">
                                    <i class="fas fa-plus"></i> Add Period
                                </button>
                                <small class="help-text">Add multiple periods for breaks (e.g., 9:00-14:00 and 17:00-21:00)</small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <button type="submit" class="btn btn-primary">Save Business Hours</button>
            </form>
        </div>

    <?php elseif ($currentTab === 'special_days'): ?>
        <!-- Special Days -->
        <div class="form-container">
            <?php
            $action = $_GET['action'] ?? '';
            $editDate = $_GET['date'] ?? '';
            ?>

            <?php if ($action === 'add' || ($action === 'edit' && $editDate)): ?>
                <!-- Add/Edit Special Day Form -->
                <div class="content-card">
                    <div class="form-section">
                        <h3><?= $action === 'add' ? 'Add Special Day' : 'Edit Special Day' ?></h3>

                        <form method="POST">
                            <input type="hidden" name="action" value="save_special_day">
                            <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">
                            <?php if ($action === 'edit'): ?>
                                <input type="hidden" name="edit_date" value="<?= htmlspecialchars($editDate) ?>">
                            <?php endif; ?>

                            <div class="form-group">
                                <label for="special_date">Date *</label>
                                <input type="date" id="special_date" name="special_date" required
                                    value="<?= $action === 'edit' ? htmlspecialchars($editDate) : '' ?>"
                                    <?= $action === 'edit' ? 'readonly' : '' ?>>
                            </div>

                            <div class="form-group">
                                <label for="special_note">Note</label>
                                <input type="text" id="special_note" name="special_note"
                                    placeholder="e.g., Christmas Day, New Year's Eve"
                                    value="<?= $action === 'edit' && isset($specialDays[$editDate]['note']) ? htmlspecialchars($specialDays[$editDate]['note']) : '' ?>">
                            </div>

                            <div class="form-group">
                                <label>Schedule Type</label>
                                <div class="radio-group">
                                    <label class="radio-option">
                                        <input type="radio" name="schedule_type" value="closed"
                                            <?= ($action === 'edit' && empty($specialDays[$editDate]['hours'])) ? 'checked' : '' ?>>
                                        <span>Closed</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="schedule_type" value="custom"
                                            <?= ($action === 'add' || ($action === 'edit' && !empty($specialDays[$editDate]['hours']))) ? 'checked' : '' ?>>
                                        <span>Custom Hours</span>
                                    </label>
                                </div>
                            </div>

                            <div id="custom-hours" style="<?= ($action === 'edit' && empty($specialDays[$editDate]['hours'])) ? 'display: none;' : '' ?>">
                                <div class="form-group">
                                    <label>Business Hours</label>
                                    <div id="time-periods">
                                        <?php if ($action === 'edit' && !empty($specialDays[$editDate]['hours'])): ?>
                                            <?php foreach ($specialDays[$editDate]['hours'] as $i => $period): ?>
                                                <div class="time-period-row">
                                                    <input type="time" name="start_times[]" value="<?= htmlspecialchars($period['start']) ?>" required>
                                                    <span>to</span>
                                                    <input type="time" name="end_times[]" value="<?= htmlspecialchars($period['end']) ?>" required>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeTimePeriod(this)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="time-period-row">
                                                <input type="time" name="start_times[]" value="09:00" required>
                                                <span>to</span>
                                                <input type="time" name="end_times[]" value="17:00" required>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="removeTimePeriod(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline" onclick="addTimePeriod()">
                                        <i class="fas fa-plus"></i> Add Time Period
                                    </button>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <?= $action === 'add' ? 'Add Special Day' : 'Update Special Day' ?>
                                </button>
                                <a href="/store-admin/?page=********&tab=special_days" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <!-- Special Days List -->
                <h3>Special Days & Holiday Hours</h3>

                <div class="special-days-list">
                    <?php if (empty($specialDays)): ?>
                        <div class="empty-state">
                            <i class="fas fa-calendar-day"></i>
                            <p>No special days configured</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($specialDays as $date => $config): ?>
                            <div class="special-day-item">
                                <div class="special-day-info">
                                    <strong><?php echo date('M j, Y', strtotime($date)); ?></strong>
                                    <?php if (!empty($config['note'])): ?>
                                        <span class="special-day-note"><?php echo htmlspecialchars($config['note']); ?></span>
                                    <?php endif; ?>

                                    <?php if (empty($config['hours'])): ?>
                                        <span class="status-badge status-cancelled">Closed</span>
                                    <?php else: ?>
                                        <div class="special-day-hours">
                                            <?php foreach ($config['hours'] as $period): ?>
                                                <span class="time-period"><?php echo $period['start']; ?> - <?php echo $period['end']; ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="special-day-actions">
                                    <button class="btn-icon btn-edit" onclick="editSpecialDay('<?php echo $date; ?>')" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="delete_special_day">
                                        <input type="hidden" name="delete_date" value="<?php echo $date; ?>">
                                        <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">
                                        <button type="submit" class="btn-icon btn-delete" title="Delete"
                                            onclick="return confirm('Delete this special day?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <button class="btn btn-primary" onclick="addSpecialDay()">
                    <i class="fas fa-plus"></i> Add Special Day
                </button>
        </div>
    <?php endif; ?>
</div>

<?php elseif ($currentTab === 'email'): ?>
    <!-- Email & SMS ******** -->
    <div class="form-container">
        <h3>Email & SMS Configuration</h3>
        <form method="POST">
            <input type="hidden" name="action" value="save_communications">
            <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

            <!-- Email ******** Section -->
            <div class="********-section">
                <h4><i class="fas fa-envelope"></i> Email ********</h4>

                <div class="form-group">
                    <div class="toggle-item">
                        <div class="toggle-label">
                            <div class="toggle-label-text">Enable SMTP Email</div>
                            <div class="toggle-label-description">Use custom SMTP server for sending emails instead of default mail function</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" name="smtp_enabled"
                                <?php echo $********['smtp_enabled'] ? 'checked' : ''; ?>
                                onchange="toggleSmtp********()">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <div id="smtp-********" style="<?php echo $********['smtp_enabled'] ? '' : 'display: none;'; ?>">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="smtp_host">SMTP Host</label>
                            <input type="text" id="smtp_host" name="smtp_host"
                                value="<?php echo htmlspecialchars($********['smtp_host']); ?>">
                        </div>
                        <div class="form-group">
                            <label for="smtp_port">SMTP Port</label>
                            <input type="number" id="smtp_port" name="smtp_port"
                                value="<?php echo $********['smtp_port']; ?>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="smtp_security">Security</label>
                            <select id="smtp_security" name="smtp_security">
                                <option value="none" <?php echo $********['smtp_security'] === 'none' ? 'selected' : ''; ?>>None</option>
                                <option value="ssl" <?php echo $********['smtp_security'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                <option value="tls" <?php echo $********['smtp_security'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="smtp_username">Username</label>
                            <input type="text" id="smtp_username" name="smtp_username"
                                value="<?php echo htmlspecialchars($********['smtp_username']); ?>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="smtp_password">Password</label>
                        <input type="password" id="smtp_password" name="smtp_password"
                            placeholder="<?php echo $********['smtp_password'] ? '••••••••' : 'Enter password'; ?>">
                    </div>
                </div>
            </div>

            <!-- SMS ******** Section -->
            <div class="********-section">
                <h4><i class="fas fa-sms"></i> SMS ********</h4>

                <div class="form-group">
                    <div class="toggle-item">
                        <div class="toggle-label">
                            <div class="toggle-label-text">Enable SMS Messaging</div>
                            <div class="toggle-label-description">Send SMS notifications and verification codes to customers</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" name="sms_enabled"
                                <?php echo $********['sms_enabled'] ? 'checked' : ''; ?>
                                onchange="toggleSms********()">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <div id="sms-********" style="<?php echo $********['sms_enabled'] ? '' : 'display: none;'; ?>">
                    <div class="form-group">
                        <label for="default_country_code">Default Country Code</label>
                        <input type="text" id="default_country_code" name="default_country_code"
                            value="<?php echo htmlspecialchars($********['default_country_code']); ?>"
                            placeholder="+30">
                        <small>Used when phone numbers don't include country code (Greece: +30)</small>
                    </div>



                    <div class="form-group">
                        <label for="sms_provider">SMS Provider</label>
                        <select id="sms_provider" name="sms_provider" onchange="toggleSmsProvider********()">
                            <option value="none" <?php echo $********['sms_provider'] === 'none' ? 'selected' : ''; ?>>Select Provider</option>
                            <option value="twilio" <?php echo $********['sms_provider'] === 'twilio' ? 'selected' : ''; ?>>Twilio</option>
                            <option value="nexmo" <?php echo $********['sms_provider'] === 'nexmo' ? 'selected' : ''; ?>>Nexmo/Vonage</option>
                            <option value="apifon" <?php echo $********['sms_provider'] === 'apifon' ? 'selected' : ''; ?>>Apifon (Greece)</option>
                            <option value="smstools" <?php echo $********['sms_provider'] === 'smstools' ? 'selected' : ''; ?>>SMS-Tool (Greece)</option>
                            <option value="smsto" <?php echo $********['sms_provider'] === 'smsto' ? 'selected' : ''; ?>>SMS.to (Global)</option>
                        </select>
                    </div>

                    <!-- Twilio ******** -->
                    <div id="twilio-********" class="provider-********" style="<?php echo $********['sms_provider'] === 'twilio' ? '' : 'display: none;'; ?>">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="twilio_account_sid">Account SID</label>
                                <input type="text" id="twilio_account_sid" name="twilio_account_sid"
                                    value="<?php echo htmlspecialchars($********['twilio_account_sid']); ?>"
                                    placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
                            </div>
                            <div class="form-group">
                                <label for="twilio_auth_token">Auth Token</label>
                                <input type="text" id="twilio_auth_token" name="twilio_auth_token"
                                    value="<?php echo htmlspecialchars($********['twilio_auth_token']); ?>"
                                    placeholder="Enter your auth token">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="twilio_from_number">From Number</label>
                            <input type="text" id="twilio_from_number" name="twilio_from_number"
                                value="<?php echo htmlspecialchars($********['twilio_from_number']); ?>"
                                placeholder="+306947767541">
                            <small>Your verified phone number</small>
                        </div>


                    </div>

                    <!-- Nexmo ******** -->
                    <div id="nexmo-********" class="provider-********" style="<?php echo $********['sms_provider'] === 'nexmo' ? '' : 'display: none;'; ?>">
                        <h5>Nexmo (Vonage) Configuration</h5>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="nexmo_api_key">API Key</label>
                                <input type="text" id="nexmo_api_key" name="nexmo_api_key"
                                    value="<?php echo htmlspecialchars($********['nexmo_api_key']); ?>">
                            </div>
                            <div class="form-group">
                                <label for="nexmo_api_secret">API Secret</label>
                                <input type="text" id="nexmo_api_secret" name="nexmo_api_secret"
                                    value="<?php echo htmlspecialchars($********['nexmo_api_secret']); ?>"
                                    placeholder="Enter API secret">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="nexmo_from_number">From Number/Name</label>
                            <input type="text" id="nexmo_from_number" name="nexmo_from_number"
                                value="<?php echo htmlspecialchars($********['nexmo_from_number']); ?>"
                                placeholder="YourBrand or +**********">
                        </div>
                    </div>

                    <!-- Apifon ******** -->
                    <div id="apifon-********" class="provider-********" style="<?php echo $********['sms_provider'] === 'apifon' ? '' : 'display: none;'; ?>">
                        <h5>Apifon Configuration (Greek SMS Provider)</h5>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="apifon_api_key">API Key</label>
                                <input type="text" id="apifon_api_key" name="apifon_api_key"
                                    value="<?php echo htmlspecialchars($********['apifon_api_key']); ?>"
                                    placeholder="Your Apifon API key">
                            </div>
                            <div class="form-group">
                                <label for="apifon_api_token">API Token</label>
                                <input type="text" id="apifon_api_token" name="apifon_api_token"
                                    value="<?php echo htmlspecialchars($********['apifon_api_token']); ?>"
                                    placeholder="Your Apifon API token (if different from API key)">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="apifon_client_id">Client ID</label>
                                <input type="text" id="apifon_client_id" name="apifon_client_id"
                                    value="<?php echo htmlspecialchars($********['apifon_client_id']); ?>"
                                    placeholder="Your Apifon Client ID (if required)">
                            </div>
                            <div class="form-group">
                                <label for="apifon_username">Username</label>
                                <input type="text" id="apifon_username" name="apifon_username"
                                    value="<?php echo htmlspecialchars($********['apifon_username']); ?>"
                                    placeholder="Your Apifon username (if required)">
                            </div>
                            <div class="form-group">
                                <label for="apifon_sender">Sender Name</label>
                                <input type="text" id="apifon_sender" name="apifon_sender"
                                    value="<?php echo htmlspecialchars($********['apifon_sender']); ?>"
                                    placeholder="YourBrand (max 11 chars)">
                                <small>Sender name as it appears to recipients</small>
                            </div>
                        </div>
                        <div class="info-box">
                            <p><strong>Apifon Setup:</strong></p>
                            <p>1. Sign up at <a href="https://www.apifon.com/" target="_blank">apifon.com</a></p>
                            <p>2. Get your API key from the dashboard</p>
                            <p>3. Configure sender name (up to 11 characters)</p>
                            <p>4. Add credits to your account</p>
                        </div>
                    </div>

                    <!-- SMS-Tool ******** -->
                    <div id="smstools-********" class="provider-********" style="<?php echo $********['sms_provider'] === 'smstools' ? '' : 'display: none;'; ?>">
                        <h5>SMS-Tool Configuration (Greek SMS Provider)</h5>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="smstools_username">Username</label>
                                <input type="text" id="smstools_username" name="smstools_username"
                                    value="<?php echo htmlspecialchars($********['smstools_username']); ?>"
                                    placeholder="Your SMStools username">
                            </div>
                            <div class="form-group">
                                <label for="smstools_password">Password</label>
                                <input type="text" id="smstools_password" name="smstools_password"
                                    value="<?php echo htmlspecialchars($********['smstools_password']); ?>"
                                    placeholder="Enter password">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="smstools_sender">Sender Name</label>
                            <input type="text" id="smstools_sender" name="smstools_sender"
                                value="<?php echo htmlspecialchars($********['smstools_sender']); ?>"
                                placeholder="YourBrand">
                            <small>Sender name as it appears to recipients</small>
                        </div>
                        <div class="info-box">
                            <p><strong>SMS-Tool Setup:</strong></p>
                            <p>1. Sign up at <a href="https://www.sms-tool.gr/" target="_blank">sms-tool.gr</a></p>
                            <p>2. Get your username and password</p>
                            <p>3. Configure sender name</p>
                            <p>4. Add credits to your account</p>
                        </div>
                    </div>

                    <!-- SMS.to ******** -->
                    <div id="smsto-********" class="provider-********" style="<?php echo $********['sms_provider'] === 'smsto' ? '' : 'display: none;'; ?>">
                        <h5>SMS.to Configuration (Global SMS Provider)</h5>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="smsto_api_key">API Key</label>
                                <input type="text" id="smsto_api_key" name="smsto_api_key"
                                    value="<?php echo htmlspecialchars($********['smsto_api_key']); ?>"
                                    placeholder="Your SMS.to API key">
                            </div>
                            <div class="form-group">
                                <label for="smsto_sender">Sender Name</label>
                                <input type="text" id="smsto_sender" name="smsto_sender"
                                    value="<?php echo htmlspecialchars($********['smsto_sender']); ?>"
                                    placeholder="Your business name (max 11 chars)">
                            </div>
                        </div>
                        <div class="info-box">
                            <p><strong>SMS.to Setup:</strong></p>
                            <ol>
                                <li>Sign up at <a href="https://sms.to" target="_blank">sms.to</a></li>
                                <li>Get your API key from the dashboard</li>
                                <li>Add credits to your account</li>
                                <li>Test SMS sending</li>
                            </ol>
                            <p><strong>Features:</strong> Global coverage, competitive rates, reliable delivery</p>
                        </div>
                    </div>


                </div>
            </div>

            <button type="submit" class="btn btn-primary">Save Email & SMS ********</button>
        </form>
    </div>

<?php elseif ($currentTab === 'admin'): ?>
    <!-- Admin ******** -->
    <div class="form-container">
        <h3>Admin Account ********</h3>
        <form method="POST">
            <input type="hidden" name="action" value="save_admin">
            <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

            <div class="form-group">
                <label for="admin_username">Admin Username</label>
                <input type="text" id="admin_username" name="admin_username"
                    value="<?php echo htmlspecialchars($********['admin_username']); ?>" required>
            </div>

            <div class="form-group">
                <label for="admin_password">New Password</label>
                <input type="password" id="admin_password" name="admin_password"
                    placeholder="Leave blank to keep current password">
                <small>Password must be at least 8 characters long</small>
            </div>

            <div class="form-group">
                <label for="admin_password_confirm">Confirm New Password</label>
                <input type="password" id="admin_password_confirm" name="admin_password_confirm"
                    placeholder="Confirm new password">
            </div>

            <button type="submit" class="btn btn-primary">Update Admin ********</button>
        </form>
    </div>
<?php endif; ?>
</div>

<script>
    function toggleDayHours(day) {
        const checkbox = document.querySelector(`input[name="${day}_enabled"]`);
        const timesDiv = document.getElementById(`${day}_times`);

        if (checkbox.checked) {
            timesDiv.style.display = 'block';
        } else {
            timesDiv.style.display = 'none';
        }
    }

    function addPeriod(day) {
        const container = document.getElementById(`${day}_periods`);
        const periods = container.querySelectorAll('.time-period');
        const newIndex = periods.length;

        const newPeriod = document.createElement('div');
        newPeriod.className = 'time-period';
        newPeriod.setAttribute('data-period', newIndex);

        newPeriod.innerHTML = `
        <input type="time" name="${day}_start[]" value="17:00" required>
        <span>to</span>
        <input type="time" name="${day}_end[]" value="21:00" required>
        <button type="button" class="btn-remove-period" onclick="removePeriod('${day}', ${newIndex})">
            <i class="fas fa-times"></i>
        </button>
    `;

        container.appendChild(newPeriod);
    }

    function removePeriod(day, index) {
        const container = document.getElementById(`${day}_periods`);
        const period = container.querySelector(`[data-period="${index}"]`);
        if (period) {
            period.remove();

            // Reindex remaining periods
            const remainingPeriods = container.querySelectorAll('.time-period');
            remainingPeriods.forEach((period, newIndex) => {
                period.setAttribute('data-period', newIndex);
                const removeBtn = period.querySelector('.btn-remove-period');
                if (removeBtn) {
                    removeBtn.setAttribute('onclick', `removePeriod('${day}', ${newIndex})`);
                }
            });
        }
    }

    function toggleSmtp********() {
        const checkbox = document.querySelector('input[name="smtp_enabled"]');
        const ********Div = document.getElementById('smtp-********');

        if (checkbox.checked) {
            ********Div.style.display = 'block';
        } else {
            ********Div.style.display = 'none';
        }
    }

    function toggleSms********() {
        const checkbox = document.querySelector('input[name="sms_enabled"]');
        const ********Div = document.getElementById('sms-********');

        if (checkbox.checked) {
            ********Div.style.display = 'block';
        } else {
            ********Div.style.display = 'none';
        }
    }

    function toggleSmsProvider********() {
        const provider = document.getElementById('sms_provider').value;
        const providers = ['twilio', 'nexmo', 'apifon', 'smstools', 'smsto'];

        providers.forEach(p => {
            const element = document.getElementById(p + '-********');
            if (element) {
                element.style.display = provider === p ? 'block' : 'none';
            }
        });
    }

    // Trial mode functionality removed - was causing issues with non-Twilio providers

    function toggleVerificationOptions() {
        // Only run if we're on the booking tab and elements exist
        const verificationMethodEl = document.getElementById('verification_method');
        if (!verificationMethodEl) return;

        const verificationMethod = verificationMethodEl.value;
        const smsOption = document.getElementById('sms-verification-option');
        const smsWarning = document.getElementById('sms-verification-warning');

        if (!smsOption || !smsWarning) return;

        const smsEnabled = <?php echo $********['sms_enabled'] === '1' ? 'true' : 'false'; ?>;

        // Check if SMS is enabled
        if (!smsEnabled) {
            // Disable SMS option and show warning if selected
            smsOption.disabled = true;
            if (verificationMethod === 'sms') {
                smsWarning.style.display = 'block';
                // Optionally reset to email
                document.getElementById('verification_method').value = 'email';
            } else {
                smsWarning.style.display = 'none';
            }
        } else {
            // Enable SMS option and hide warning
            smsOption.disabled = false;
            smsWarning.style.display = 'none';
        }
    }

    // Initialize verification options on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Only run if we're on the booking tab
        if (document.getElementById('verification_method')) {
            toggleVerificationOptions();
        }
    });

    function addSpecialDay() {
        // Redirect to dedicated add special day page
        window.location.href = '/store-admin/?page=********&tab=special_days&action=add';
    }

    function editSpecialDay(date) {
        // Redirect to dedicated edit special day page
        window.location.href = '/store-admin/?page=********&tab=special_days&action=edit&date=' + encodeURIComponent(date);
    }

    // Special days form functionality
    function addTimePeriod() {
        const container = document.getElementById('time-periods');
        const newRow = document.createElement('div');
        newRow.className = 'time-period-row';
        newRow.innerHTML = `
            <input type="time" name="start_times[]" value="09:00" required>
            <span>to</span>
            <input type="time" name="end_times[]" value="17:00" required>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeTimePeriod(this)">
                <i class="fas fa-trash"></i>
            </button>
        `;
        container.appendChild(newRow);
    }

    function removeTimePeriod(button) {
        const container = document.getElementById('time-periods');
        if (container.children.length > 1) {
            button.parentElement.remove();
        }
    }

    // Handle schedule type radio buttons
    document.addEventListener('DOMContentLoaded', function() {
        const scheduleTypeRadios = document.querySelectorAll('input[name="schedule_type"]');
        const customHoursDiv = document.getElementById('custom-hours');

        if (scheduleTypeRadios.length && customHoursDiv) {
            scheduleTypeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'closed') {
                        customHoursDiv.style.display = 'none';
                    } else {
                        customHoursDiv.style.display = 'block';
                    }
                });
            });
        }
    });

    function initializeSpecialDaysInModal() {
        // Handle schedule type radio buttons in modal (try multiple selectors)
        const scheduleTypeRadios = document.querySelectorAll('input[name="schedule_type"]');
        const customHoursDiv = document.querySelector('#custom-hours') || document.getElementById('custom-hours');

        // console.log('Found radios:', scheduleTypeRadios.length);
        // console.log('Found custom hours div:', customHoursDiv);

        if (scheduleTypeRadios.length > 0 && customHoursDiv) {
            scheduleTypeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    // console.log('Radio changed to:', this.value);
                    if (this.value === 'general_hours') {
                        customHoursDiv.style.display = 'block';
                        // console.log('Showing custom hours');
                    } else {
                        customHoursDiv.style.display = 'none';
                        // console.log('Hiding custom hours');
                    }
                });
            });

            // Initialize based on current selection
            const selectedRadio = document.querySelector('input[name="schedule_type"]:checked');
            if (selectedRadio && selectedRadio.value === 'general_hours') {
                customHoursDiv.style.display = 'block';
                // console.log('Initial show of custom hours');
            }
        } else {
            // console.warn('Could not find schedule type radios or custom hours div');
        }
    }

    function addSpecialPeriod() {
        const container = document.getElementById('special_periods');
        const periods = container.querySelectorAll('.time-period');
        const newIndex = periods.length;

        const newPeriod = document.createElement('div');
        newPeriod.className = 'time-period';
        newPeriod.setAttribute('data-period', newIndex);

        newPeriod.innerHTML = `
        <input type="time" name="general_start[]" value="17:00" required>
        <span>to</span>
        <input type="time" name="general_end[]" value="21:00" required>
        <button type="button" class="btn-remove-period" onclick="removeSpecialPeriod(${newIndex})">
            <i class="fas fa-times"></i>
        </button>
    `;

        container.appendChild(newPeriod);
    }

    function removeSpecialPeriod(index) {
        const container = document.getElementById('special_periods');
        const period = container.querySelector(`[data-period="${index}"]`);
        if (period) {
            period.remove();

            // Reindex remaining periods
            const remainingPeriods = container.querySelectorAll('.time-period');
            remainingPeriods.forEach((period, newIndex) => {
                period.setAttribute('data-period', newIndex);
                const removeBtn = period.querySelector('.btn-remove-period');
                if (removeBtn) {
                    removeBtn.setAttribute('onclick', `removeSpecialPeriod(${newIndex})`);
                }
            });
        }
    }

    // Old getSpecialDayForm function removed - now using inline forms
</script>

<style>
    .********-tabs {
        display: flex;
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 30px;
        overflow-x: auto;
    }

    .tab-link {
        padding: 15px 20px;
        text-decoration: none;
        color: var(--text-muted);
        border-bottom: 3px solid transparent;
        white-space: nowrap;
    }

    .tab-link:hover {
        color: var(--primary-color);
    }

    .tab-link.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
    }

    .tab-link i {
        margin-right: 8px;
    }

    /* Legacy checkbox styles - kept for backward compatibility */
    .checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .checkbox-group label {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .working-hours-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 20px;
    }

    @media (min-width: 640px) {
        .working-hours-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
    }

    @media (min-width: 1024px) {
        .working-hours-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (min-width: 1400px) {
        .working-hours-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    .day-hours {
        padding: 16px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        background: var(--white);
        box-shadow: var(--shadow-sm);
    }

    @media (max-width: 640px) {
        .day-hours {
            padding: 12px;
        }

        .day-header .toggle-item {
            padding: 6px 0 !important;
        }

        .toggle-label-text {
            font-size: 14px;
            font-weight: 600;
        }
    }

    .day-header {
        margin-bottom: 10px;
    }

    .day-header label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 0;
    }

    .day-times {
        margin-left: 24px;
    }

    .periods-container {
        margin: 10px 0;
    }

    .time-period {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .time-period input[type="time"] {
        width: 120px;
        padding: 8px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
    }

    @media (max-width: 640px) {
        .time-period {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;
            padding: 10px;
        }

        .time-period input[type="time"] {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            /* Prevent zoom on iOS */
        }

        .time-period span {
            text-align: center;
            font-weight: 500;
            color: var(--gray-600);
        }

        .btn-remove-period {
            align-self: center;
            min-width: 44px;
        }
    }

    .btn-remove-period {
        background: #dc3545;
        color: white;
        border: none;
        padding: 6px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
    }

    .btn-remove-period:hover {
        background: #c82333;
    }

    .help-text {
        display: block;
        margin-top: 5px;
        color: #6c757d;
        font-size: 12px;
    }

    .special-days-list {
        margin-bottom: 20px;
        display: grid;
        gap: 16px;
    }

    .special-day-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 16px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        background: var(--white);
        box-shadow: var(--shadow-sm);
    }

    @media (max-width: 640px) {
        .special-day-item {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;
            padding: 12px;
        }

        .special-day-info {
            margin-bottom: 8px;
        }

        .special-day-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .special-day-actions .btn {
            flex: 1;
            min-height: 44px;
        }
    }

    .special-day-info {
        flex: 1;
    }

    .special-day-note {
        color: var(--text-muted);
        font-style: italic;
        margin-left: 10px;
    }

    .special-day-hours {
        margin-top: 5px;
    }

    .special-day-hours .time-period {
        display: inline-block;
        background: var(--light-color);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.875rem;
        margin-right: 5px;
    }

    .special-day-actions {
        display: flex;
        gap: 5px;
    }

    .radio-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .radio-group label {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .time-period-row {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .time-period-row input[type="time"] {
        padding: 0.375rem 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 0.875rem;
    }

    .time-period-row span {
        color: #666;
        font-size: 0.875rem;
    }

    @media (max-width: 640px) {
        .time-period-row {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;
            padding: 10px;
        }

        .time-period-row input[type="time"] {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            /* Prevent zoom on iOS */
        }

        .time-period-row span {
            text-align: center;
            font-weight: 500;
        }

        .time-period-row .btn {
            align-self: center;
            min-width: 44px;
        }
    }

    .form-actions {
        display: flex;
        gap: 10px;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #eee;
    }

    /* ******** section dividers */
    .********-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 2px solid var(--light-bg);
    }

    .********-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .********-section-title {
        color: var(--dark-color);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
    }

    /* SMS ******** Styles */
    .********-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #fafafa;
    }

    .********-section h4 {
        margin: 0 0 20px 0;
        color: #333;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .********-section h5 {
        margin: 20px 0 15px 0;
        color: #666;
        font-size: 16px;
    }

    .provider-******** {
        margin-top: 15px;
        padding: 15px;
        background: white;
        border-radius: 5px;
        border-left: 4px solid #007bff;
    }

    .info-box {
        margin-top: 15px;
        padding: 12px;
        background: #e8f4fd;
        border: 1px solid #bee5eb;
        border-radius: 4px;
        font-size: 14px;
    }

    .info-box p {
        margin: 5px 0;
    }

    .info-box a {
        color: #007bff;
        text-decoration: none;
    }

    .info-box a:hover {
        text-decoration: underline;
    }

    .test-mode-info {
        margin-top: 15px;
        padding: 15px;
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        border-left: 4px solid #f39c12;
    }

    .test-mode-info h5 {
        margin: 0 0 10px 0;
        color: #856404;
    }

    .test-mode-info ul {
        margin: 0;
        padding-left: 20px;
    }

    .test-mode-info li {
        margin: 5px 0;
        color: #856404;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .alert-error {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .alert i {
        font-size: 18px;
    }

    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeaa7;
    }

    #sms-verification-option:disabled {
        color: #6c757d;
        background-color: #e9ecef;
        opacity: 0.65;
    }

    @media (max-width: 768px) {
        .********-tabs {
            flex-wrap: wrap;
        }

        .form-row {
            flex-direction: column;
        }

        .special-day-item {
            flex-direction: column;
            gap: 10px;
        }

        .special-day-actions {
            justify-content: center;
        }
    }
</style>