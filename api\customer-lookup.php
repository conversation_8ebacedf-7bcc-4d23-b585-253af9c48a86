<?php

/**
 * Customer Lookup API
 * Lookup existing customers by email for verification-only booking mode
 */

require_once __DIR__ . '/../shared/config.php';
require_once __DIR__ . '/../shared/database.php';
require_once __DIR__ . '/../shared/tenant.php';
require_once __DIR__ . '/../shared/functions.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

function successResponse(array $data): void
{
    echo json_encode(['success' => true, 'data' => $data]);
    exit;
}

function errorResponse(string $message, int $code = 400): void
{
    http_response_code($code);
    echo json_encode(['success' => false, 'error' => $message]);
    exit;
}

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    errorResponse('Invalid JSON input');
}

$email = trim($input['email'] ?? '');

if (empty($email)) {
    errorResponse('Email is required');
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    errorResponse('Invalid email format');
}

try {
    $db = TenantManager::getDatabase();
    
    // Look up customer by email
    $customer = $db->fetchRow(
        "SELECT id, name, email, phone, language FROM customers WHERE email = :email LIMIT 1",
        [':email' => $email]
    );
    
    if ($customer) {
        // Customer found - return customer data
        successResponse([
            'found' => true,
            'customer' => [
                'id' => $customer['id'],
                'name' => $customer['name'],
                'email' => $customer['email'],
                'phone' => $customer['phone'] ?? '',
                'language' => $customer['language'] ?? 'el'
            ]
        ]);
    } else {
        // Customer not found
        successResponse([
            'found' => false,
            'customer' => null
        ]);
    }
    
} catch (Exception $e) {
    logActivity("Customer lookup failed: " . $e->getMessage(), 'error');
    errorResponse('Lookup failed: ' . $e->getMessage());
}
