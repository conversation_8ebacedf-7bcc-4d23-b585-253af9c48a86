<?php

/**
 * Settings Controller
 * Handles all ******** operations
 */

/**
 * Handle ******** form submission
 */
function handleSettingsForm(array $data, Database $db): array
{
    $action = $data['action'] ?? '';

    // Debug log
    error_log("Settings form submitted - Action: $action, Tenant: " . TenantManager::getCurrentTenant());

    // Debug: Log Twilio credentials being submitted
    if ($action === 'save_communications') {
        error_log("Twilio credentials in POST: SID=" . ($data['twilio_account_sid'] ?? 'NOT_SET') . ", Token=" . (isset($data['twilio_auth_token']) ? 'SET(len=' . strlen($data['twilio_auth_token']) . ')' : 'NOT_SET'));
    }

    // Validate CSRF token (temporarily disabled for testing)
    // if (!Application::verifyCsrfToken($data['csrf_token'] ?? '')) {
    //     return ['success' => false, 'error' => 'Invalid request token'];
    // }

    switch ($action) {
        case 'save_general':
            return handleGeneralSettingsForm($data, $db);
        case 'save_services':
            return handleServiceSettingsForm($data, $db);
        case 'save_business_hours':
            return handleBusinessHoursForm($data, $db);
        case 'save_special_days':
            return handleSpecialDaysForm($data, $db);
        case 'save_special_day':
            return handleSaveSpecialDay($data, $db);
        case 'delete_special_day':
            return handleDeleteSpecialDay($data, $db);
        case 'save_email':
            return handleEmailSettingsForm($data, $db);
        case 'save_communications':
            return handleCommunicationsSettingsForm($data, $db);
        case 'save_admin':
            return handleAdminSettingsForm($data, $db);
        default:
            return ['success' => false, 'error' => 'Unknown ******** action'];
    }
}

/**
 * Handle general ******** form
 */
function handleGeneralSettingsForm(array $data, Database $db): array
{
    try {
        $********ToSave = [
            'business_name' => Application::********($data['business_name'] ?? ''),
            'business_email' => Application::********($data['business_email'] ?? ''),
            'business_phone' => Application::********($data['business_phone'] ?? ''),
            'business_address' => Application::********($data['business_address'] ?? ''),
            'booking_advance_days' => (int)($data['booking_advance_days'] ?? 60),
            'global_buffer_time' => (int)($data['global_buffer_time'] ?? 15),

            'require_phone' => isset($data['require_phone']) ? '1' : '0',
            'default_language' => $data['default_language'] ?? 'el',
            'verification_method' => $data['verification_method'] ?? 'email'
        ];

        $db->beginTransaction();

        foreach ($********ToSave as $key => $value) {
            $db->query(
                "INSERT OR REPLACE INTO ******** (key, value, updated_at) VALUES (:key, :value, :updated_at)",
                [
                    ':key' => $key,
                    ':value' => $value,
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );
        }

        $db->commit();

        // Refresh admin translation if default language was changed
        if (isset($********ToSave['default_language'])) {
            AdminTranslation::refresh($db);
        }

        return [
            'success' => true,
            'message' => 'General ******** saved successfully',
            'redirect' => '/store-admin/?page=********&tab=general'
        ];
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Handle service ******** form
 */
function handleServiceSettingsForm(array $data, Database $db): array
{
    try {
        // No general service ******** to save anymore
        // Individual service ******** are handled in the services controller

        return [
            'success' => true,
            'message' => 'Service ******** saved successfully',
            'redirect' => '/store-admin/?page=********&tab=services'
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Handle business hours form
 */
function handleBusinessHoursForm(array $data, Database $db): array
{
    try {
        // Business hours
        $businessHours = [];
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        foreach ($days as $day) {
            if (isset($data["{$day}_enabled"])) {
                $periods = [];

                // Handle multiple periods (arrays)
                $starts = $data["{$day}_start"] ?? [];
                $ends = $data["{$day}_end"] ?? [];

                // Ensure arrays
                if (!is_array($starts)) $starts = [$starts];
                if (!is_array($ends)) $ends = [$ends];

                // Build periods from start/end pairs
                for ($i = 0; $i < count($starts) && $i < count($ends); $i++) {
                    if (!empty($starts[$i]) && !empty($ends[$i])) {
                        $periods[] = [
                            'start' => $starts[$i],
                            'end' => $ends[$i]
                        ];
                    }
                }

                $businessHours[$day] = $periods;
            } else {
                $businessHours[$day] = [];
            }
        }

        $db->beginTransaction();

        // Store business hours in ******** as JSON (supports multiple periods)
        $db->query(
            "INSERT OR REPLACE INTO ******** (key, value, updated_at) VALUES (:key, :value, :updated_at)",
            [
                ':key' => 'business_hours',
                ':value' => json_encode($businessHours),
                ':updated_at' => date('Y-m-d H:i:s')
            ]
        );

        // Also update working_hours table for backward compatibility (first period only, no periods column)
        $dayMapping = ['monday' => 1, 'tuesday' => 2, 'wednesday' => 3, 'thursday' => 4, 'friday' => 5, 'saturday' => 6, 'sunday' => 7];

        foreach ($businessHours as $day => $periods) {
            $dayOfWeek = $dayMapping[$day];
            $existing = $db->fetchRow("SELECT day_of_week FROM working_hours WHERE day_of_week = :day", [':day' => $dayOfWeek]);

            if (!empty($periods) && isset($periods[0])) {
                // Has working hours - use first period for backward compatibility
                $startTime = $periods[0]['start'] ?? '09:00';
                $endTime = $periods[0]['end'] ?? '17:00';

                if ($existing) {
                    $db->query(
                        "UPDATE working_hours SET start_time = :start, end_time = :end, is_active = 1, updated_at = :updated WHERE day_of_week = :day",
                        [
                            ':day' => $dayOfWeek,
                            ':start' => $startTime,
                            ':end' => $endTime,
                            ':updated' => date('Y-m-d H:i:s')
                        ]
                    );
                } else {
                    $db->query(
                        "INSERT INTO working_hours (day_of_week, start_time, end_time, is_active, updated_at) VALUES (:day, :start, :end, 1, :updated)",
                        [
                            ':day' => $dayOfWeek,
                            ':start' => $startTime,
                            ':end' => $endTime,
                            ':updated' => date('Y-m-d H:i:s')
                        ]
                    );
                }
            } else {
                // No working hours - mark as inactive
                if ($existing) {
                    $db->query(
                        "UPDATE working_hours SET is_active = 0, updated_at = :updated WHERE day_of_week = :day",
                        [
                            ':day' => $dayOfWeek,
                            ':updated' => date('Y-m-d H:i:s')
                        ]
                    );
                }
            }
        }

        $db->commit();
        return [
            'success' => true,
            'message' => 'Business hours saved successfully',
            'redirect' => '/store-admin/?page=********&tab=business_hours'
        ];
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Handle email ******** form
 */
function handleEmailSettingsForm(array $data, Database $db): array
{
    try {
        $********ToSave = [
            'smtp_enabled' => isset($data['smtp_enabled']) ? '1' : '0',
            'smtp_host' => Application::********($data['smtp_host'] ?? ''),
            'smtp_port' => (int)($data['smtp_port'] ?? 587),
            'smtp_security' => $data['smtp_security'] ?? 'tls',
            'smtp_username' => Application::********($data['smtp_username'] ?? ''),
        ];

        // Only update password if provided
        if (!empty($data['smtp_password'])) {
            $********ToSave['smtp_password'] = $data['smtp_password'];
        }

        $db->beginTransaction();

        foreach ($********ToSave as $key => $value) {
            $db->query(
                "INSERT OR REPLACE INTO ******** (key, value, updated_at) VALUES (:key, :value, :updated_at)",
                [
                    ':key' => $key,
                    ':value' => $value,
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );
        }

        $db->commit();
        return [
            'success' => true,
            'message' => 'Email ******** saved successfully',
            'redirect' => '/store-admin/?page=********&tab=email'
        ];
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Handle communications (email & SMS) ******** form
 */
function handleCommunicationsSettingsForm(array $data, Database $db): array
{
    try {
        // Debug log
        error_log("Communications ******** - sms_test_mode in data: " . (isset($data['sms_test_mode']) ? 'YES (' . $data['sms_test_mode'] . ')' : 'NO'));
        error_log("Communications ******** - POST data: " . print_r($data, true));

        $********ToSave = [
            // Email ********
            'smtp_enabled' => isset($data['smtp_enabled']) ? '1' : '0',
            'smtp_host' => Application::********($data['smtp_host'] ?? ''),
            'smtp_port' => (int)($data['smtp_port'] ?? 587),
            'smtp_security' => $data['smtp_security'] ?? 'tls',
            'smtp_username' => Application::********($data['smtp_username'] ?? ''),

            // SMS ********
            'sms_enabled' => isset($data['sms_enabled']) ? '1' : '0',
            'sms_provider' => $data['sms_provider'] ?? 'none',
            'default_country_code' => Application::********($data['default_country_code'] ?? '+1'),


            // Twilio ********
            'twilio_account_sid' => Application::********($data['twilio_account_sid'] ?? ''),
            'twilio_auth_token' => Application::********($data['twilio_auth_token'] ?? ''),
            'twilio_from_number' => Application::********($data['twilio_from_number'] ?? ''),


            // Nexmo ********
            'nexmo_api_key' => Application::********($data['nexmo_api_key'] ?? ''),
            'nexmo_from_number' => Application::********($data['nexmo_from_number'] ?? ''),

            // Apifon ******** (Greek provider)
            'apifon_api_key' => Application::********($data['apifon_api_key'] ?? ''),
            'apifon_api_token' => Application::********($data['apifon_api_token'] ?? ''),
            'apifon_client_id' => Application::********($data['apifon_client_id'] ?? ''),
            'apifon_username' => Application::********($data['apifon_username'] ?? ''),
            'apifon_sender' => Application::********($data['apifon_sender'] ?? ''),

            // SMStools ******** (Greek provider)
            'smstools_username' => Application::********($data['smstools_username'] ?? ''),
            'smstools_sender' => Application::********($data['smstools_sender'] ?? ''),

            // SMS.to ******** (Global provider)
            'smsto_api_key' => Application::********($data['smsto_api_key'] ?? ''),
            'smsto_sender' => Application::********($data['smsto_sender'] ?? ''),

            // Custom SMS ********
            'custom_sms_webhook' => Application::********($data['custom_sms_webhook'] ?? ''),
        ];

        // Only update passwords/secrets if provided
        if (!empty($data['smtp_password'])) {
            $********ToSave['smtp_password'] = $data['smtp_password'];
        }

        if (!empty($data['nexmo_api_secret'])) {
            $********ToSave['nexmo_api_secret'] = $data['nexmo_api_secret'];
        }
        if (!empty($data['smstools_password'])) {
            $********ToSave['smstools_password'] = $data['smstools_password'];
        }
        if (!empty($data['custom_sms_api_key'])) {
            $********ToSave['custom_sms_api_key'] = $data['custom_sms_api_key'];
        }

        $db->beginTransaction();

        foreach ($********ToSave as $key => $value) {
            $db->query(
                "INSERT OR REPLACE INTO ******** (key, value, updated_at) VALUES (:key, :value, :updated_at)",
                [
                    ':key' => $key,
                    ':value' => $value,
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );
        }

        $db->commit();

        // Debug: Verify what was actually saved
        $savedSid = Application::getSetting('twilio_account_sid', 'NOT_FOUND');
        $savedToken = Application::getSetting('twilio_auth_token', 'NOT_FOUND');
        error_log("After save - SID: $savedSid, Token: " . ($savedToken !== 'NOT_FOUND' ? 'SET(len=' . strlen($savedToken) . ')' : 'NOT_FOUND'));

        return [
            'success' => true,
            'message' => 'Email & SMS ******** saved successfully',
            'redirect' => '/store-admin/?page=********&tab=email'
        ];
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Handle admin ******** form
 */
function handleAdminSettingsForm(array $data, Database $db): array
{
    try {
        $username = trim($data['admin_username'] ?? '');
        $password = $data['admin_password'] ?? '';
        $passwordConfirm = $data['admin_password_confirm'] ?? '';

        if (empty($username)) {
            return ['success' => false, 'error' => 'Username is required'];
        }

        $********ToSave = [
            'admin_username' => $username
        ];

        // Handle password change
        if (!empty($password)) {
            if (strlen($password) < 8) {
                return ['success' => false, 'error' => 'Password must be at least 8 characters long'];
            }

            if ($password !== $passwordConfirm) {
                return ['success' => false, 'error' => 'Passwords do not match'];
            }

            $********ToSave['admin_password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        $db->beginTransaction();

        foreach ($********ToSave as $key => $value) {
            $db->query(
                "INSERT OR REPLACE INTO ******** (key, value, updated_at) VALUES (:key, :value, :updated_at)",
                [
                    ':key' => $key,
                    ':value' => $value,
                    ':updated_at' => date('Y-m-d H:i:s')
                ]
            );
        }

        $db->commit();

        // Update session username if changed
        if (isset($_SESSION['admin_username'])) {
            $_SESSION['admin_username'] = $username;
        }

        return [
            'success' => true,
            'message' => 'Admin ******** updated successfully',
            'redirect' => '/store-admin/?page=********&tab=admin'
        ];
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Handle save special day
 */
function handleSaveSpecialDay(array $data, Database $db): array
{
    try {
        $date = $data['special_date'] ?? '';
        $note = trim($data['special_note'] ?? '');
        $scheduleType = $data['schedule_type'] ?? 'closed';
        $editDate = $data['edit_date'] ?? '';

        if (empty($date)) {
            return ['success' => false, 'error' => 'Date is required'];
        }

        // Get existing special days
        $existingData = Application::getSetting('special_days', '');
        $specialDays = $existingData ? json_decode($existingData, true) : [];

        // If editing, remove the old entry
        if (!empty($editDate) && $editDate !== $date && isset($specialDays[$editDate])) {
            unset($specialDays[$editDate]);
        }

        $config = [];

        if (!empty($note)) {
            $config['note'] = $note;
        }

        if ($scheduleType === 'closed') {
            // Closed all day - no hours
            $config['hours'] = [];
        } elseif ($scheduleType === 'custom') {
            // Custom hours
            $hours = [];
            $starts = $data['start_times'] ?? [];
            $ends = $data['end_times'] ?? [];

            for ($i = 0; $i < count($starts); $i++) {
                if (!empty($starts[$i]) && !empty($ends[$i])) {
                    $hours[] = [
                        'start' => $starts[$i],
                        'end' => $ends[$i]
                    ];
                }
            }

            $config['hours'] = $hours;
        }

        $specialDays[$date] = $config;

        $db->beginTransaction();

        $db->query(
            "INSERT OR REPLACE INTO ******** (key, value, updated_at) VALUES (:key, :value, :updated_at)",
            [
                ':key' => 'special_days',
                ':value' => json_encode($specialDays),
                ':updated_at' => date('Y-m-d H:i:s')
            ]
        );

        $db->commit();

        $action = !empty($editDate) ? 'updated' : 'added';
        return [
            'success' => true,
            'message' => "Special day {$action} successfully",
            'redirect' => '/store-admin/?page=********&tab=special_days'
        ];
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Handle delete special day
 */
function handleDeleteSpecialDay(array $data, Database $db): array
{
    try {
        $date = $data['delete_date'] ?? '';

        if (empty($date)) {
            return ['success' => false, 'error' => 'Date is required'];
        }

        // Get existing special days
        $existingData = Application::getSetting('special_days', '');
        $specialDays = $existingData ? json_decode($existingData, true) : [];

        if (!isset($specialDays[$date])) {
            return ['success' => false, 'error' => 'Special day not found'];
        }

        unset($specialDays[$date]);

        $db->beginTransaction();

        $db->query(
            "INSERT OR REPLACE INTO ******** (key, value, updated_at) VALUES (:key, :value, :updated_at)",
            [
                ':key' => 'special_days',
                ':value' => json_encode($specialDays),
                ':updated_at' => date('Y-m-d H:i:s')
            ]
        );

        $db->commit();

        return [
            'success' => true,
            'message' => 'Special day deleted successfully',
            'redirect' => '/store-admin/?page=********&tab=special_days'
        ];
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Handle special days form (legacy text format)
 */
function handleSpecialDaysForm(array $data, Database $db): array
{
    try {
        $specialDaysText = trim($data['special_days_text'] ?? '');
        $specialDays = [];

        if (!empty($specialDaysText)) {
            $lines = explode("\n", $specialDaysText);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line)) continue;

                // Parse line format: 2024-12-25 09:00-14:00 18:00-20:00 (Note)
                if (preg_match('/^(\d{4}-\d{2}-\d{2})(.*)$/', $line, $matches)) {
                    $date = $matches[1];
                    $rest = trim($matches[2]);

                    $config = [];
                    $hours = [];
                    $note = '';

                    // Extract note in parentheses
                    if (preg_match('/\(([^)]+)\)/', $rest, $noteMatch)) {
                        $note = $noteMatch[1];
                        $rest = str_replace($noteMatch[0], '', $rest);
                    }

                    // Extract time periods
                    if (preg_match_all('/(\d{2}:\d{2})-(\d{2}:\d{2})/', $rest, $timeMatches, PREG_SET_ORDER)) {
                        foreach ($timeMatches as $timeMatch) {
                            $hours[] = [
                                'start' => $timeMatch[1],
                                'end' => $timeMatch[2]
                            ];
                        }
                    }

                    $config['hours'] = $hours;
                    if (!empty($note)) {
                        $config['note'] = $note;
                    }

                    $specialDays[$date] = $config;
                }
            }
        }

        $db->beginTransaction();

        $db->query(
            "INSERT OR REPLACE INTO ******** (key, value, updated_at) VALUES (:key, :value, :updated_at)",
            [
                ':key' => 'special_days',
                ':value' => json_encode($specialDays),
                ':updated_at' => date('Y-m-d H:i:s')
            ]
        );

        $db->commit();
        return [
            'success' => true,
            'message' => 'Special days saved successfully',
            'redirect' => '/store-admin/?page=********&tab=special_days'
        ];
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
