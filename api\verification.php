<?php

/**
 * Verification API Endpoint
 * Handles verification code validation and resending
 */

// Include dependencies if not already loaded
if (!class_exists('Config')) {
    require_once __DIR__ . '/../shared/config.php';
    require_once __DIR__ . '/../shared/database.php';
    require_once __DIR__ . '/../shared/tenant_manager.php';
    require_once __DIR__ . '/../shared/functions.php';

    // Initialize systems
    Config::init();
    TenantManager::init();

    // Set JSON response headers
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

    // Handle preflight requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit;
    }
}

require_once __DIR__ . '/../shared/smtp.php';
require_once __DIR__ . '/../shared/verification.php';

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    errorResponse('Invalid JSON input');
}

$action = $input['action'] ?? null;

switch ($action) {
    case 'verify':
        verifyCode($input);
        break;

    case 'resend':
        resendCode($input);
        break;

    default:
        errorResponse('Invalid action');
}

function verifyCode(array $input): void
{
    $reservationId = $input['reservation_id'] ?? null;
    $verificationCode = $input['verification_code'] ?? null;

    if (!$reservationId || !$verificationCode) {
        errorResponse('Missing required fields');
    }

    try {
        $db = TenantManager::getDatabase();

        // Get reservation
        $reservation = $db->fetchRow(
            "SELECT * FROM reservations WHERE id = :id",
            [':id' => $reservationId]
        );

        if (!$reservation) {
            errorResponse('Reservation not found');
        }

        // Check if already verified (status = 'confirmed')
        if ($reservation['status'] === 'confirmed') {
            errorResponse('Reservation already verified');
        }

        // Get verification code from verification_codes table
        $verification = $db->fetchRow(
            "SELECT * FROM verification_codes
             WHERE type = 'booking' AND identifier = :reservation_id AND code = :code
             AND expires_at > :now AND used_at IS NULL
             ORDER BY created_at DESC LIMIT 1",
            [
                ':reservation_id' => $reservationId,
                ':code' => $verificationCode,
                ':now' => date('Y-m-d H:i:s')
            ]
        );

        if (!$verification) {
            errorResponse('Invalid or expired verification code');
        }

        // Mark verification code as used
        $db->query(
            "UPDATE verification_codes SET used_at = :used WHERE id = :id",
            [
                ':used' => date('Y-m-d H:i:s'),
                ':id' => $verification['id']
            ]
        );

        // Update reservation status
        $db->query(
            "UPDATE reservations SET status = 'confirmed', updated_at = :updated
             WHERE id = :id",
            [
                ':updated' => date('Y-m-d H:i:s'),
                ':id' => $reservationId
            ]
        );

        // Send confirmation email
        sendConfirmationEmail($reservation);

        // Send admin notification for confirmed reservation
        try {
            // Get full reservation details for admin notification
            $reservationDetails = $db->fetchRow(
                "SELECT r.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
                 s.name as service_name, s.duration, e.name as employee_name
                 FROM reservations r
                 JOIN customers c ON r.customer_id = c.id
                 JOIN services s ON r.service_id = s.id
                 JOIN employees e ON r.employee_id = e.id
                 WHERE r.id = :id",
                [':id' => $reservationId]
            );

            if ($reservationDetails) {
                $mailer = new SMTPMailer();
                $mailer->sendAdminReservationNotification([
                    'reservation_id' => $reservationDetails['id'],
                    'customer_name' => $reservationDetails['customer_name'],
                    'customer_email' => $reservationDetails['customer_email'],
                    'customer_phone' => $reservationDetails['customer_phone'] ?? '',
                    'service_name' => $reservationDetails['service_name'],
                    'date' => $reservationDetails['date'],
                    'start_time' => $reservationDetails['start_time'],
                    'duration' => $reservationDetails['duration'],
                    'employee_name' => $reservationDetails['employee_name'],
                    'price' => $reservationDetails['price'],
                    'status' => 'confirmed', // Status is now confirmed
                    'notes' => $reservationDetails['notes'] ?? '',
                    'created_at' => $reservationDetails['created_at']
                ]);
            }
        } catch (Exception $e) {
            // Don't fail the verification if admin notification fails
            error_log("Admin notification failed: " . $e->getMessage());
        }

        successResponse([
            'verified' => true,
            'message' => 'Booking confirmed successfully!'
        ]);
    } catch (Exception $e) {
        logActivity("Verification failed: " . $e->getMessage(), 'error');
        errorResponse('Verification failed: ' . $e->getMessage());
    }
}

function resendCode(array $input): void
{
    $reservationId = $input['reservation_id'] ?? null;

    if (!$reservationId) {
        errorResponse('Missing reservation ID');
    }

    try {
        $db = TenantManager::getDatabase();

        // Get reservation with customer details
        $reservation = $db->fetchRow(
            "SELECT r.*, c.email, c.phone, c.name FROM reservations r
             JOIN customers c ON r.customer_id = c.id
             WHERE r.id = :id",
            [':id' => $reservationId]
        );

        if (!$reservation) {
            errorResponse('Reservation not found');
        }

        // Check if already verified
        if ($reservation['status'] === 'confirmed') {
            errorResponse('Reservation already verified');
        }

        // Get verification method setting
        $verificationMethod = $db->fetchColumn(
            "SELECT value FROM settings WHERE key = 'verification_method'",
            []
        ) ?: 'email';

        // Get business name
        $businessName = $db->fetchColumn(
            "SELECT value FROM settings WHERE key = 'business_name'",
            []
        ) ?: 'Booking System';

        // Use unified verification system to resend
        $verification = new VerificationManager($db);
        $result = $verification->sendVerificationByMethod(
            $reservation['email'],
            $reservation['phone'],
            $businessName
        );

        if ($result['success']) {
            $message = $verificationMethod === 'email'
                ? 'New verification code sent to your email.'
                : 'New verification code sent to your phone.';

            successResponse([
                'resent' => true,
                'method' => $verificationMethod,
                'message' => $message
            ]);
        } else {
            errorResponse('Failed to resend verification code: ' . $result['message']);
        }
    } catch (Exception $e) {
        logActivity("Resend verification failed: " . $e->getMessage(), 'error');
        errorResponse('Failed to resend verification code: ' . $e->getMessage());
    }
}

function sendConfirmationEmail(array $reservation): void
{
    try {
        $db = TenantManager::getDatabase();

        // Get full booking details
        $bookingDetails = $db->fetchRow(
            "SELECT r.*, c.name as customer_name, c.email, c.language, s.name as service_name,
             s.duration, s.price, e.name as employee_name
             FROM reservations r
             JOIN customers c ON r.customer_id = c.id
             JOIN services s ON r.service_id = s.id
             JOIN employees e ON r.employee_id = e.id
             WHERE r.id = :id",
            [':id' => $reservation['id']]
        );

        if (!$bookingDetails) {
            return;
        }

        $businessName = $db->fetchColumn(
            "SELECT value FROM settings WHERE key = 'business_name'",
            []
        ) ?: 'Booking System';

        $customerLanguage = $bookingDetails['language'] ?? 'el';

        $mailer = new SMTPMailer();
        $mailer->sendBookingConfirmation($bookingDetails['email'], [
            'business_name' => $businessName,
            'customer_name' => $bookingDetails['customer_name'],
            'service_name' => $bookingDetails['service_name'],
            'date' => formatDate($bookingDetails['date'], 'l, F j, Y'),
            'time' => formatTime($bookingDetails['start_time']),
            'duration' => $bookingDetails['duration'],
            'price' => $bookingDetails['price'],
            'employee_name' => $bookingDetails['employee_name']
        ], $customerLanguage);

        logActivity("Booking confirmation email sent to: " . $bookingDetails['email'], 'info');
    } catch (Exception $e) {
        logActivity("Failed to send confirmation email: " . $e->getMessage(), 'error');
    }
}
