<?php

/**
 * Entity Card Component
 * Reusable card component for displaying entities (customers, services, employees, etc.)
 */

function renderEntityCard($entity, $type = 'generic', $options = [])
{
    // Use consistent card classes from the new design system
    $cardClass = 'entity-card card--interactive';
    $statusClass = '';

    // Add type-specific styling using consistent naming
    switch ($type) {
        case 'customer':
            $cardClass .= ' entity-card--customer';
            $statusClass = isset($entity['status']) && $entity['status'] === 'active' ? 'entity-card--active' : 'entity-card--inactive';
            break;
        case 'service':
            $cardClass .= ' entity-card--service';
            $statusClass = isset($entity['active']) && $entity['active'] ? 'entity-card--active' : 'entity-card--inactive';
            break;
        case 'employee':
            $cardClass .= ' entity-card--employee';
            $statusClass = isset($entity['active']) && $entity['active'] ? 'entity-card--active' : 'entity-card--inactive';
            break;
        case 'reservation':
            $cardClass .= ' entity-card--reservation';
            if (isset($entity['status'])) {
                switch ($entity['status']) {
                    case 'confirmed':
                        $statusClass = 'entity-card--active';
                        break;
                    case 'cancelled':
                        $statusClass = 'entity-card--error';
                        break;
                    case 'pending':
                        $statusClass = 'entity-card--warning';
                        break;
                }
            }
            break;
        case 'category':
            $cardClass .= ' entity-card--category';
            $statusClass = isset($entity['active']) && $entity['active'] ? 'entity-card--active' : 'entity-card--inactive';
            break;
    }

    $cardClass .= ' ' . $statusClass;
    $entityId = $entity['id'] ?? '';
    $showCheckbox = $options['show_checkbox'] ?? true;

    ob_start();
?>

    <div class="<?= htmlspecialchars($cardClass) ?> entity-card--clickable" data-id="<?= htmlspecialchars($entityId) ?>" data-type="<?= htmlspecialchars($type) ?>" onclick="viewEntity('<?= htmlspecialchars($entityId) ?>', '<?= htmlspecialchars($type) ?>')" style="cursor: pointer;">

        <!-- Card Header -->
        <div class="card__header">
            <div class="flex items-start gap-3">
                <?php if ($showCheckbox): ?>
                    <div class="flex-shrink-0 mt-1" onclick="event.stopPropagation();">
                        <input type="checkbox" class="entity-select" value="<?= htmlspecialchars($entityId) ?>"
                            aria-label="Select <?= htmlspecialchars($type) ?>">
                    </div>
                <?php endif; ?>

                <div class="flex-1 min-w-0">
                    <h3 class="entity-title">
                        <?= htmlspecialchars($entity['name'] ?? $entity['title'] ?? 'Unnamed') ?>
                    </h3>

                    <?php if (isset($entity['subtitle']) || isset($entity['email'])): ?>
                        <p class="entity-subtitle">
                            <?= htmlspecialchars($entity['subtitle'] ?? $entity['email'] ?? '') ?>
                        </p>
                    <?php endif; ?>
                </div>

                <!-- Status Indicator -->
                <div class="flex-shrink-0">
                    <div class="entity-status-indicator <?= $statusClass === 'entity-card--active' ? 'active' : ($statusClass === 'entity-card--warning' ? 'warning' : 'inactive') ?>"></div>
                </div>
            </div>
        </div>

        <!-- Card Body -->
        <div class="card__body">
            <?php
            // Render type-specific content
            switch ($type) {
                case 'customer':
                    renderCustomerCardContent($entity);
                    break;
                case 'service':
                    renderServiceCardContent($entity);
                    break;
                case 'employee':
                    renderEmployeeCardContent($entity);
                    break;
                case 'reservation':
                    renderReservationCardContent($entity);
                    break;
                case 'category':
                    renderCategoryCardContent($entity);
                    break;
                default:
                    renderGenericCardContent($entity);
            }
            ?>
        </div>

        <!-- Card Actions -->
        <?php if (isset($options['show_actions']) && $options['show_actions']): ?>
            <div class="entity-card-actions">
                <?php echo renderEntityActions($entity, $type); ?>
            </div>
        <?php endif; ?>

    </div>

<?php
    return ob_get_clean();
}

function renderCustomerCardContent($customer)
{
?>
    <div class="entity-info space-y-3">
        <?php if (isset($customer['email'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-envelope text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Email</span>
                    <a href="mailto:<?= htmlspecialchars($customer['email']) ?>" class="info-value contact-link">
                        <?= htmlspecialchars($customer['email']) ?>
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($customer['phone'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-phone text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Phone</span>
                    <a href="tel:<?= htmlspecialchars($customer['phone']) ?>" class="info-value contact-link">
                        <?= htmlspecialchars($customer['phone']) ?>
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($customer['language'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-globe text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Language</span>
                    <span class="info-value">
                        <span class="language-badge"><?= htmlspecialchars(strtoupper($customer['language'])) ?></span>
                    </span>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Customer Stats -->
    <div class="entity-stats">
        <div class="stat-item">
            <div class="stat-value"><?= $customer['total_visits'] ?? 0 ?></div>
            <div class="stat-label">Visits</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">€<?= number_format($customer['total_spent'] ?? 0, 2) ?></div>
            <div class="stat-label">Spent</div>
        </div>
        <div class="stat-item">
            <div class="stat-value"><?= isset($customer['last_visit']) ? date('M j', strtotime($customer['last_visit'])) : 'Never' ?></div>
            <div class="stat-label">Last Visit</div>
        </div>
    </div>
<?php
}

function renderServiceCardContent($service)
{
?>
    <div class="entity-info space-y-3">
        <?php if (isset($service['description']) && !empty($service['description'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-info-circle text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Description</span>
                    <span class="info-value"><?= htmlspecialchars(substr($service['description'], 0, 80)) ?><?= strlen($service['description']) > 80 ? '...' : '' ?></span>
                </div>
            </div>
        <?php endif; ?>

        <div class="service-pricing flex justify-between items-center">
            <?php if (isset($service['duration'])): ?>
                <span class="duration-badge"><?= htmlspecialchars($service['duration']) ?> min</span>
            <?php endif; ?>

            <?php if (isset($service['price'])): ?>
                <span class="price-badge">€<?= number_format($service['price'], 0) ?></span>
            <?php endif; ?>
        </div>

        <?php if (isset($service['category_name'])): ?>
            <div class="service-category">
                <?php if ($service['category_name'] !== 'Uncategorized'): ?>
                    <span class="category-badge" <?php if (isset($service['category_color']) && !empty($service['category_color'])): ?>
                        style="background-color: <?= htmlspecialchars($service['category_color']) ?>; color: white;"
                        <?php endif; ?>>
                        <?= htmlspecialchars($service['category_name']) ?>
                    </span>
                <?php else: ?>
                    <span class="category-badge" style="background-color: var(--gray-100); color: var(--gray-600);">
                        Uncategorized
                    </span>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($service['preparation_time']) && $service['preparation_time'] > 0 || isset($service['cleanup_time']) && $service['cleanup_time'] > 0): ?>
            <div class="service-timing">
                <div class="timing-breakdown flex gap-2 text-xs">
                    <?php if (isset($service['preparation_time']) && $service['preparation_time'] > 0): ?>
                        <span class="timing-item prep">
                            <i class="fas fa-clock"></i> Prep: <?= $service['preparation_time'] ?>min
                        </span>
                    <?php endif; ?>
                    <span class="timing-item main">
                        <i class="fas fa-cut"></i> Service: <?= $service['duration'] ?>min
                    </span>
                    <?php if (isset($service['cleanup_time']) && $service['cleanup_time'] > 0): ?>
                        <span class="timing-item cleanup">
                            <i class="fas fa-broom"></i> Cleanup: <?= $service['cleanup_time'] ?>min
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Service Stats -->
    <div class="entity-stats">
        <div class="stat-item">
            <div class="stat-value">€<?= number_format($service['price'], 0) ?></div>
            <div class="stat-label">Price</div>
        </div>
        <div class="stat-item">
            <div class="stat-value"><?= $service['duration'] ?>min</div>
            <div class="stat-label">Duration</div>
        </div>
        <div class="stat-item">
            <div class="stat-value"><?= $service['total_duration'] ?? $service['duration'] ?>min</div>
            <div class="stat-label">Total Time</div>
        </div>
    </div>
<?php
}

function renderEmployeeCardContent($employee)
{
?>
    <!-- Employee Avatar -->
    <?php if (isset($employee['avatar_color'])): ?>
        <div class="employee-avatar-section">
            <div class="employee-avatar" style="background-color: <?= htmlspecialchars($employee['avatar_color']) ?>;">
                <?php
                $initial = mb_substr($employee['name'], 0, 1, 'UTF-8');
                echo $initial ? mb_strtoupper($initial, 'UTF-8') : '<i class="fas fa-user"></i>';
                ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="entity-info space-y-3">
        <?php if (isset($employee['position'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-user-tag text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Position</span>
                    <span class="info-value position-badge"><?= htmlspecialchars($employee['position']) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($employee['email']) && !empty($employee['email'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-envelope text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Email</span>
                    <a href="mailto:<?= htmlspecialchars($employee['email']) ?>" class="info-value contact-link">
                        <?= htmlspecialchars($employee['email']) ?>
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($employee['phone']) && !empty($employee['phone'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-phone text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Phone</span>
                    <a href="tel:<?= htmlspecialchars($employee['phone']) ?>" class="info-value contact-link">
                        <?= htmlspecialchars($employee['phone']) ?>
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Employee Skills (Hidden by default, expandable) -->
    <?php if (isset($employee['services']) && !empty($employee['services'])): ?>
        <div class="employee-skills" id="employee-skills-<?= $employee['id'] ?>" style="display: none;">
            <div class="skills-header">
                <span class="skills-label">Skills (<?= count($employee['services']) ?>)</span>
            </div>
            <div class="skills-list">
                <?php foreach (array_slice($employee['services'], 0, 3) as $service): ?>
                    <span class="skill-badge"><?= htmlspecialchars($service['name']) ?></span>
                <?php endforeach; ?>
                <?php if (count($employee['services']) > 3): ?>
                    <span class="skill-badge more">+<?= count($employee['services']) - 3 ?> more</span>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Employee Stats -->
    <div class="entity-stats">
        <div class="stat-item stat-item-clickable" onclick="event.stopPropagation(); toggleEmployeeSkills('<?= htmlspecialchars($employee['id']) ?>')" title="Click to show/hide skills">
            <div class="stat-value">
                <?= $employee['services_count'] ?? 0 ?>
                <i class="fas fa-chevron-down stat-arrow" id="employee-arrow-<?= $employee['id'] ?>"></i>
            </div>
            <div class="stat-label">Services</div>
        </div>
        <div class="stat-item">
            <div class="stat-value"><?= $employee['bookings_this_month'] ?? 0 ?></div>
            <div class="stat-label">This Month</div>
        </div>
        <div class="stat-item">
            <div class="stat-value"><?= $employee['rating'] ?? 'N/A' ?><?= isset($employee['rating']) ? '⭐' : '' ?></div>
            <div class="stat-label">Rating</div>
        </div>
    </div>
<?php
}

function renderReservationCardContent($reservation)
{
?>
    <div class="entity-info space-y-3">
        <div class="reservation-datetime">
            <div class="date-value"><?= date('M j, Y', strtotime($reservation['date'])) ?></div>
            <div class="time-value"><?= date('H:i', strtotime($reservation['start_time'])) ?></div>
        </div>

        <?php if (isset($reservation['service_name'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-cut text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Service</span>
                    <span class="info-value service-name"><?= htmlspecialchars($reservation['service_name']) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($reservation['employee_name'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-user text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Employee</span>
                    <span class="info-value employee-name"><?= htmlspecialchars($reservation['employee_name']) ?></span>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Reservation Stats -->
    <div class="entity-stats">
        <div class="stat-item">
            <div class="stat-value">€<?= number_format($reservation['price'] ?? 0, 2) ?></div>
            <div class="stat-label">Price</div>
        </div>
        <div class="stat-item">
            <div class="stat-value"><?= $reservation['duration'] ?? 0 ?> min</div>
            <div class="stat-label">Duration</div>
        </div>
        <div class="stat-item">
            <div class="stat-value status-badge status-<?= $reservation['status'] ?? 'pending' ?>">
                <?= ucfirst($reservation['status'] ?? 'Pending') ?>
            </div>
            <div class="stat-label">Status</div>
        </div>
    </div>
<?php
}

function renderCategoryCardContent($category)
{
?>
    <!-- Category Icon -->
    <?php if (isset($category['color']) && isset($category['icon'])): ?>
        <div class="category-icon-section">
            <div class="category-icon" style="background-color: <?= htmlspecialchars($category['color']) ?>;">
                <i class="<?= htmlspecialchars($category['icon']) ?>"></i>
            </div>
        </div>
    <?php endif; ?>

    <div class="entity-info space-y-3">
        <?php if (isset($category['name_en']) && !empty($category['name_en'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-language text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">English Name</span>
                    <span class="info-value"><?= htmlspecialchars($category['name_en']) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($category['description']) && !empty($category['description'])): ?>
            <div class="info-row">
                <div class="info-icon">
                    <i class="fas fa-info-circle text-gray-400"></i>
                </div>
                <div class="info-content">
                    <span class="info-label">Description</span>
                    <span class="info-value"><?= htmlspecialchars(substr($category['description'], 0, 80)) ?><?= strlen($category['description']) > 80 ? '...' : '' ?></span>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Category Services (Hidden by default, expandable) -->
    <?php if (isset($category['services']) && !empty($category['services'])): ?>
        <div class="category-services" id="category-services-<?= $category['id'] ?>" style="display: none;">
            <div class="services-header">
                <span class="services-label">Services (<?= count($category['services']) ?>)</span>
            </div>
            <div class="services-list">
                <?php foreach (array_slice($category['services'], 0, 3) as $service): ?>
                    <div class="service-item">
                        <span class="service-name"><?= htmlspecialchars($service['name']) ?></span>
                        <span class="service-price">€<?= number_format($service['price'], 0) ?></span>
                    </div>
                <?php endforeach; ?>
                <?php if (count($category['services']) > 3): ?>
                    <div class="service-item more">
                        <span class="service-name">+<?= count($category['services']) - 3 ?> more services</span>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Category Stats -->
    <div class="entity-stats">
        <div class="stat-item stat-item-clickable" onclick="event.stopPropagation(); toggleCategoryServices('<?= htmlspecialchars($category['id']) ?>')" title="Click to show/hide services">
            <div class="stat-value">
                <?= $category['services_count'] ?? 0 ?>
                <i class="fas fa-chevron-down stat-arrow" id="category-arrow-<?= $category['id'] ?>"></i>
            </div>
            <div class="stat-label">Services</div>
        </div>
        <div class="stat-item">
            <div class="stat-value"><?= $category['sort_order'] ?? 0 ?></div>
            <div class="stat-label">Sort Order</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">
                <div class="color-swatch" style="background-color: <?= htmlspecialchars($category['color']) ?>;"></div>
            </div>
            <div class="stat-label">Color</div>
        </div>
    </div>
<?php
}

function renderGenericCardContent($entity)
{
?>
    <div class="entity-info">
        <?php if (isset($entity['description'])): ?>
            <p class="text-gray-600"><?= htmlspecialchars($entity['description']) ?></p>
        <?php endif; ?>

        <?php if (isset($entity['created_at'])): ?>
            <div class="text-sm text-gray-500 mt-2">
                Created: <?= date('M j, Y', strtotime($entity['created_at'])) ?>
            </div>
        <?php endif; ?>
    </div>
<?php
}

function renderEntityActions($entity, $type)
{
    $entityId = $entity['id'] ?? '';
?>
    <div class="action-buttons">
        <button class="btn btn-primary btn-sm" onclick="view<?= ucfirst($type) ?>('<?= htmlspecialchars($entityId) ?>')"
            title="View <?= ucfirst($type) ?>" aria-label="View <?= ucfirst($type) ?>">
            <i class="fas fa-eye"></i> View
        </button>
        <button class="btn btn-secondary btn-sm" onclick="edit<?= ucfirst($type) ?>('<?= htmlspecialchars($entityId) ?>')"
            title="Edit <?= ucfirst($type) ?>" aria-label="Edit <?= ucfirst($type) ?>">
            <i class="fas fa-edit"></i> Edit
        </button>
        <?php if ($type === 'customer'): ?>
            <button class="btn btn-info btn-sm" onclick="sendEmailToCustomer('<?= htmlspecialchars($entityId) ?>')"
                title="Send Email" aria-label="Send Email to Customer">
                <i class="fas fa-envelope"></i> Email
            </button>
        <?php endif; ?>
        <?php if ($type !== 'reservation'): ?>
            <button class="btn btn-danger btn-sm" onclick="delete<?= ucfirst($type) ?>('<?= htmlspecialchars($entityId) ?>')"
                title="Delete <?= ucfirst($type) ?>" aria-label="Delete <?= ucfirst($type) ?>">
                <i class="fas fa-trash"></i> Delete
            </button>
        <?php endif; ?>
    </div>
<?php
}
?>