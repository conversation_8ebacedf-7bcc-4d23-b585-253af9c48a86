<?php

/**
 * Admin Reservation Confirmation API
 * Allows admin to manually confirm reservations
 */

// Basic error handling
error_reporting(0);
ini_set('display_errors', 0);

require_once __DIR__ . '/../shared/config.php';
require_once __DIR__ . '/../shared/database.php';
require_once __DIR__ . '/../shared/tenant_manager.php';
require_once __DIR__ . '/../shared/functions.php';
require_once __DIR__ . '/../shared/smtp.php';

// Set JSON response headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Response functions are already defined in shared/functions.php

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    errorResponse('Invalid JSON input');
}

$reservationId = $input['reservation_id'] ?? null;
$action = $input['action'] ?? null; // 'confirm' or 'cancel'

if (!$reservationId || !$action) {
    errorResponse('Missing required fields');
}

if (!in_array($action, ['confirm', 'cancel'])) {
    errorResponse('Invalid action. Must be "confirm" or "cancel"');
}

try {
    // Initialize tenant context first
    TenantManager::init();

    $db = TenantManager::getDatabase();

    // Get reservation details
    $reservation = $db->fetchRow(
        "SELECT r.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone, c.language,
         s.name as service_name, s.duration, e.name as employee_name
         FROM reservations r
         JOIN customers c ON r.customer_id = c.id
         JOIN services s ON r.service_id = s.id
         JOIN employees e ON r.employee_id = e.id
         WHERE r.id = :id",
        [':id' => $reservationId]
    );

    if (!$reservation) {
        errorResponse('Reservation not found');
    }

    // Update reservation status
    $newStatus = ($action === 'confirm') ? 'confirmed' : 'cancelled';
    $db->query(
        "UPDATE reservations SET status = :status, updated_at = :updated
         WHERE id = :id",
        [
            ':status' => $newStatus,
            ':updated' => date('Y-m-d H:i:s'),
            ':id' => $reservationId
        ]
    );

    // Send emails if confirming
    if ($action === 'confirm') {
        // Send confirmation email to customer
        try {
            $businessName = $db->fetchColumn(
                "SELECT value FROM settings WHERE key = 'business_name'",
                []
            ) ?: 'Booking System';

            $customerLanguage = $reservation['language'] ?? 'el';

            // Prepare booking data for email template
            $bookingData = [
                'business_name' => $businessName,
                'customer_name' => $reservation['customer_name'],
                'service_name' => $reservation['service_name'],
                'date' => formatDate($reservation['date'], 'l, F j, Y'),
                'time' => formatTime($reservation['start_time']),
                'duration' => $reservation['duration'],
                'price' => $reservation['price'],
                'employee_name' => $reservation['employee_name'],
                'reservation_id' => $reservation['id']
            ];

            $mailer = new SMTPMailer();
            $emailSent = $mailer->sendBookingConfirmation($reservation['customer_email'], $bookingData, $customerLanguage);

            if ($emailSent) {
                logActivity("Admin confirmation email sent to: " . $reservation['customer_email'], 'info');
            } else {
                logActivity("Failed to send admin confirmation email to: " . $reservation['customer_email'], 'error');
            }
        } catch (Exception $e) {
            logActivity("Failed to send admin confirmation email: " . $e->getMessage(), 'error');
        }

        // Send admin notification if enabled
        try {
            $mailer = new SMTPMailer();
            $mailer->sendAdminReservationNotification([
                'reservation_id' => $reservation['id'],
                'customer_name' => $reservation['customer_name'],
                'customer_email' => $reservation['customer_email'],
                'customer_phone' => $reservation['customer_phone'] ?? '',
                'service_name' => $reservation['service_name'],
                'date' => $reservation['date'],
                'start_time' => $reservation['start_time'],
                'duration' => $reservation['duration'],
                'employee_name' => $reservation['employee_name'],
                'price' => $reservation['price'],
                'status' => 'confirmed',
                'notes' => $reservation['notes'] ?? '',
                'created_at' => $reservation['created_at']
            ]);
        } catch (Exception $e) {
            logActivity("Failed to send admin notification: " . $e->getMessage(), 'error');
        }
    }

    $message = ($action === 'confirm')
        ? 'Reservation confirmed successfully. Confirmation emails sent.'
        : 'Reservation cancelled successfully.';

    successResponse([
        'reservation_id' => $reservationId,
        'status' => $newStatus,
        'message' => $message
    ]);
} catch (Exception $e) {
    logActivity("Admin reservation action failed: " . $e->getMessage(), 'error');
    errorResponse('Failed to update reservation: ' . $e->getMessage());
}
