/* Store Admin CSS - Unified Responsive Design System */

/* Font Loading Optimization */
@font-display: swap;

/* Preload critical fonts */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

/* === CLIENT BOOKING SYSTEM STYLES === */

/* Customer Type Toggle */
.customer-type-toggle {
  margin-bottom: 1.5rem;
  text-align: center;
}

.toggle-button {
  background: var(--gray-100);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--gray-700);
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.toggle-button:hover {
  background: var(--gray-200);
  border-color: var(--gray-400);
}

.toggle-button.returning {
  background: var(--primary-50);
  border-color: var(--primary-300);
  color: var(--primary-700);
}

.toggle-button.returning:hover {
  background: var(--primary-100);
  border-color: var(--primary-400);
}

/* Returning Customer Notice */
.returning-customer-notice {
  background: var(--info-50);
  border: 1px solid var(--info-200);
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--info-700);
}

.returning-customer-notice i {
  color: var(--info-500);
}

/* Customer Lookup Status */
.customer-lookup-status {
  margin-top: 0.75rem;
  padding: 0.75rem;
  border-radius: var(--radius);
  font-size: 0.9rem;
}

.lookup-loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gray-600);
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  padding: 0.75rem;
  border-radius: var(--radius);
}

.lookup-found {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--success-700);
  background: var(--success-50);
  border: 1px solid var(--success-200);
  padding: 0.75rem;
  border-radius: var(--radius);
}

.lookup-found i {
  color: var(--success-500);
}

.lookup-not-found {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--warning-700);
  background: var(--warning-50);
  border: 1px solid var(--warning-200);
  padding: 0.75rem;
  border-radius: var(--radius);
}

.lookup-not-found i {
  color: var(--warning-500);
}

/* Confirmation Step Status Styles */
.confirmation-icon.pending {
  color: var(--warning-500);
}

.confirmation-icon.confirmed {
  color: var(--success-500);
}

:root {
  /* === DESIGN TOKENS === */

  /* Primary Color Palette */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Semantic Colors */
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;

  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;

  --error-50: #fef2f2;
  --error-100: #fecaca;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;

  --info-50: #ecfeff;
  --info-100: #cffafe;
  --info-500: #06b6d4;
  --info-600: #0891b2;
  --info-700: #0e7490;

  /* Neutral Palette */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --black: #000000;

  /* Legacy color mappings for compatibility */
  --primary-color: var(--primary-600);
  --primary-hover: var(--primary-700);
  --secondary-color: var(--gray-500);
  --success-color: var(--success);
  --danger-color: var(--error);
  --warning-color: var(--warning);
  --info-color: var(--info);
  --light-color: var(--gray-50);
  --dark-color: var(--gray-800);
  --border-color: var(--gray-200);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.25rem;
  /* 4px */
  --radius: 0.375rem;
  /* 6px */
  --radius-md: 0.5rem;
  /* 8px */
  --radius-lg: 0.75rem;
  /* 12px */
  --radius-xl: 1rem;
  /* 16px */

  /* Legacy radius mappings */
  --border-radius: var(--radius-md);
  --border-radius-lg: var(--radius-lg);

  /* === TYPOGRAPHY SYSTEM === */
  --font-family-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Helvetica Neue", Arial, sans-serif;
  --font-family-mono: "SF Mono", Monaco, Inconsolata, "Consolas", "Courier New",
    monospace;

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* === ANIMATION & TRANSITIONS === */
  --transition-fast: 0.15s ease-out;
  --transition: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* === RESPONSIVE BREAKPOINTS === */
  --breakpoint-sm: 640px;
  /* Small devices */
  --breakpoint-md: 768px;
  /* Medium devices */
  --breakpoint-lg: 1024px;
  /* Large devices */
  --breakpoint-xl: 1280px;
  /* Extra large devices */
  --breakpoint-2xl: 1536px;
  /* 2X large devices */

  /* === LAYOUT SYSTEM === */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  --header-height: 64px;
  --container-max-width: 1400px;
  --container-padding: var(--space-4);

  /* === COMPONENT VARIABLES === */
  /* Cards */
  --card-padding: var(--space-6);
  --card-border-radius: var(--radius-lg);
  --card-shadow: var(--shadow-sm);
  --card-shadow-hover: var(--shadow-md);

  /* Buttons */
  --button-padding-sm: var(--space-2) var(--space-3);
  --button-padding: var(--space-3) var(--space-4);
  --button-padding-lg: var(--space-4) var(--space-6);
  --button-border-radius: var(--radius);
  --button-font-weight: var(--font-medium);

  /* Forms */
  --form-input-padding: var(--space-3);
  --form-input-border-radius: var(--radius);
  --form-input-border-width: 1px;
  --form-input-focus-ring: 0 0 0 3px rgba(59, 130, 246, 0.1);

  /* Tables */
  --table-cell-padding: var(--space-3) var(--space-4);
  --table-header-bg: var(--gray-50);
  --table-border-color: var(--gray-200);

  /* Modals */
  --modal-backdrop: rgba(0, 0, 0, 0.5);
  --modal-border-radius: var(--radius-lg);
  --modal-shadow: var(--shadow-xl);

  /* Spacing Scale */
  --space-1: 0.25rem;
  /* 4px */
  --space-2: 0.5rem;
  /* 8px */
  --space-3: 0.75rem;
  /* 12px */
  --space-4: 1rem;
  /* 16px */
  --space-5: 1.25rem;
  /* 20px */
  --space-6: 1.5rem;
  /* 24px */
  --space-8: 2rem;
  /* 32px */
  --space-10: 2.5rem;
  /* 40px */
  --space-12: 3rem;
  /* 48px */
  --space-16: 4rem;
  /* 64px */

  /* Typography Scale */
  --text-xs: 0.75rem;
  /* 12px */
  --text-sm: 0.875rem;
  /* 14px */
  --text-base: 1rem;
  /* 16px */
  --text-lg: 1.125rem;
  /* 18px */
  --text-xl: 1.25rem;
  /* 20px */
  --text-2xl: 1.5rem;
  /* 24px */
  --text-3xl: 1.875rem;
  /* 30px */
  --text-4xl: 2.25rem;
  /* 36px */

  /* Legacy font size mappings */
  --font-size-xs: var(--text-xs);
  --font-size-sm: var(--text-sm);
  --font-size-base: var(--text-base);
  --font-size-lg: var(--text-lg);
  --font-size-xl: var(--text-xl);
  --font-size-2xl: var(--text-2xl);
  --font-size-3xl: var(--text-3xl);

  /* Responsive spacing - Mobile first */
  --container-padding: var(--space-4);
  --header-padding: var(--space-3) var(--space-4);
  --card-padding: var(--space-4);
  --button-padding: var(--space-3) var(--space-4);
}

/* Responsive Breakpoints - Card-First Approach */

/* Tablet (768px+) - Still cards, but larger */
@media (min-width: 768px) {
  :root {
    --container-padding: var(--space-5);
    --header-padding: var(--space-4) var(--space-6);
    --card-padding: var(--space-5);
    --button-padding: var(--space-3) var(--space-5);
  }
}

/* Desktop (1024px+) - Larger cards, better spacing */
@media (min-width: 1024px) {
  :root {
    --container-padding: var(--space-6);
    --header-padding: var(--space-5) var(--space-8);
    --card-padding: var(--space-6);
    --button-padding: var(--space-4) var(--space-6);
  }
}

/* Large Desktop (1200px+) - Tables become available as option */
@media (min-width: 1200px) {
  :root {
    --container-padding: var(--space-8);
    --header-padding: var(--space-6) var(--space-10);
    --card-padding: var(--space-8);
  }
}

/* Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: var(--font-family-sans);
  background-color: var(--gray-50);
  color: var(--gray-800);
  line-height: 1.6;
  font-size: var(--text-sm);
  overflow-x: hidden;
  font-display: swap;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Improved focus styles for accessibility */
:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Touch device enhancements */
.touch-device .btn:active,
.touch-device .card:active,
.touch-device .nav-link:active {
  transform: scale(0.98);
  transition: transform 0.1s ease-out;
}

/* Container system */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.container--narrow {
  max-width: 800px;
}

.container--wide {
  max-width: 1600px;
}

/* Layout System - Mobile First */
.layout {
  display: flex;
  min-height: 100vh;
  background: var(--gray-50);
}

/* Mobile: Sidebar is hidden by default */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--white);
  border-right: 1px solid var(--gray-200);
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform var(--transition);
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

.sidebar.show {
  transform: translateX(0);
}

/* Mobile: Main content takes full width */
.main-content {
  flex: 1;
  width: 100%;
  min-height: 100vh;
  background: var(--gray-50);
  transition: margin-left var(--transition);
  /* Natural scrolling */
  -webkit-overflow-scrolling: touch;
}

/* Tablet and up: Sidebar becomes fixed and visible */
@media (min-width: 768px) {
  .sidebar {
    position: fixed;
    transform: translateX(0);
    box-shadow: none;
    border-right: 1px solid var(--gray-200);
    z-index: 1000;
  }

  .main-content {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
  }

  .sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
  }

  .sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
  }
}

/* Sidebar overlay for mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

@media (min-width: 768px) {
  .sidebar-overlay {
    display: none;
  }
}

/* Sidebar Navigation */
.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-header h1 {
  color: var(--primary-color);
  font-size: 20px;
  font-weight: 700;
  white-space: nowrap;
  transition: var(--transition);
}

.sidebar.collapsed .sidebar-header h1 {
  opacity: 0;
  width: 0;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background: var(--light-color);
  color: var(--primary-color);
}

.sidebar-nav {
  padding: 16px 0;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section-title {
  padding: 0 20px 8px;
  font-size: 12px;
  font-weight: 600;
  color: var(--secondary-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: var(--transition);
}

.sidebar.collapsed .nav-section-title {
  opacity: 0;
  height: 0;
  padding: 0;
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--dark-color);
  text-decoration: none;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  background: var(--light-color);
  color: var(--primary-color);
}

.nav-link.active {
  background: var(--primary-color);
  color: white;
}

.nav-link.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-hover);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-text {
  font-weight: 500;
  white-space: nowrap;
  transition: var(--transition);
}

.sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
}

.nav-badge {
  background: var(--danger-color);
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
  transition: var(--transition);
}

.sidebar.collapsed .nav-badge {
  opacity: 0;
  width: 0;
}

/* Header */
.header {
  background: white;
  border-bottom: 1px solid var(--border-color);
  padding: var(--header-padding);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 50;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--dark-color);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  min-width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
}

.mobile-menu-btn:hover {
  background-color: var(--light-color);
}

.header-title-section {
  flex: 1;
  min-width: 0;
}

.header-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--dark-color);
  margin: 0;
}

/* Mobile header adjustments */
@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .header-title {
    font-size: var(--font-size-lg);
  }

  .breadcrumb {
    display: none;
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--secondary-color);
  font-size: 14px;
}

.breadcrumb-separator {
  color: var(--border-color);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-search {
  position: relative;
  width: 100%;
  max-width: 300px;
  min-width: 200px;
}

/* Mobile header search adjustments */
@media (max-width: 768px) {
  .header-search {
    max-width: 180px;
    min-width: 150px;
  }
}

@media (max-width: 480px) {
  .header-search {
    max-width: 140px;
    min-width: 120px;
  }

  .header-right {
    gap: 8px;
  }
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--secondary-color);
  width: 16px;
  height: 16px;
}

/* Main Content */
.main {
  padding: 24px;
  min-height: calc(100vh - 80px);
}

/* Toolbar */
.toolbar {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  box-shadow: var(--shadow);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-bar {
  position: relative;
  min-width: 300px;
}

.search-bar input {
  width: 100%;
  padding: 10px 16px 10px 44px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  transition: var(--transition);
  min-height: 44px;
  /* Touch-friendly */
}

/* Mobile form improvements */
@media (max-width: 768px) {
  .search-bar input,
  .search-input,
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  textarea,
  select {
    min-height: 48px;
    /* Larger touch targets */
    font-size: var(--font-size-base);
    /* Prevent zoom on iOS */
    padding: 12px 16px;
  }

  .search-bar input {
    padding: 12px 16px 12px 48px;
  }
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-bar .search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--secondary-color);
  width: 16px;
  height: 16px;
  z-index: 2;
  pointer-events: none;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--dark-color);
}

/* View Toggle - Only visible on large screens where tables are available */
.view-toggle {
  display: none;
  /* Hidden by default - only cards available */
}

@media (min-width: 1200px) {
  .view-toggle {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--radius);
    padding: var(--space-1);
    border: 1px solid var(--gray-200);
  }

  .view-toggle button {
    padding: var(--space-2) var(--space-3);
    border: none;
    background: none;
    color: var(--gray-600);
    border-radius: calc(var(--radius) - 2px);
    cursor: pointer;
    transition: all var(--transition);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    font-weight: 500;
    white-space: nowrap;
    min-width: 80px;
    justify-content: center;
  }

  .view-toggle button.active {
    background: var(--white);
    color: var(--primary-600);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
  }

  .view-toggle button:hover:not(.active) {
    background: var(--white);
    color: var(--gray-800);
  }

  .view-toggle button:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
  }
}

.bulk-actions {
  display: none;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  margin-bottom: 16px;
  box-shadow: var(--shadow);
}

.bulk-actions.show {
  display: flex;
}

.bulk-actions-text {
  font-weight: 500;
}

.bulk-actions-buttons {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

/* Card System - Primary UI Component */
.card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition);
  padding: 1em;
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--gray-300);
  transform: translateY(-1px);
}

.card--interactive {
  cursor: pointer;
}

.card--interactive:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-200);
  transform: translateY(-2px);
}

.card--selected {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 1px var(--primary-500), var(--shadow-md);
}

/* Card Layout Components */
.card__header {
  padding: var(--space-4) var(--space-4) 0;
  border-bottom: 1px solid var(--gray-100);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-4);
}

.card__body {
  padding: 0 var(--space-4) var(--space-4);
}

.card__footer {
  padding: var(--space-4);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-100);
  margin-top: var(--space-4);
}

/* Card variants */
.card--compact {
  padding: var(--space-3);
}

.card--spacious {
  padding: var(--space-6);
}

.card--flush {
  padding: 0;
}

.card--flush .card__header,
.card--flush .card__body,
.card--flush .card__footer {
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

/* Entity Card Content Styles */
.entity-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--gray-800);
  margin: 0 0 var(--space-1) 0;
  line-height: 1.3;
}

.entity-subtitle {
  font-size: var(--text-sm);
  color: var(--gray-500);
  margin: 0;
}

.entity-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.entity-status-indicator.active {
  background: var(--success);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.entity-status-indicator.warning {
  background: var(--warning);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.entity-status-indicator.inactive {
  background: var(--gray-400);
  box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.2);
}

/* Entity Info Rows */
.entity-info {
  margin-bottom: var(--space-4);
}

.info-row {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-2) 0;
}

.info-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
}

.info-content {
  flex: 1;
  min-width: 0;
}

.info-label {
  display: block;
  font-size: var(--text-xs);
  font-weight: 500;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.info-value {
  display: block;
  font-size: var(--text-sm);
  color: var(--gray-800);
  font-weight: 500;
}

/* Entity Stats Grid */
.entity-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-2);
  margin-top: var(--space-4);
}

.stat-item {
  text-align: center;
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
}

.stat-value {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-xs);
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

/* Entity Actions */
.entity-actions {
  display: flex;
  gap: var(--space-2);
  justify-content: center;
  padding: var(--space-4);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-100);
  margin: var(--space-4) calc(-1 * var(--space-4)) calc(-1 * var(--space-4));
}

.action-buttons {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

/* Button Icon Styles */
.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  background: var(--white);
  color: var(--gray-600);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
}

.btn-icon:hover {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-icon.btn-primary {
  border-color: var(--primary-200);
  color: var(--primary-600);
}

.btn-icon.btn-primary:hover {
  background: var(--primary-600);
  border-color: var(--primary-600);
  color: var(--white);
}

.btn-icon.btn-success {
  border-color: var(--success);
  color: var(--success);
}

.btn-icon.btn-success:hover {
  background: var(--success);
  border-color: var(--success);
  color: var(--white);
}

.btn-icon.btn-outline {
  border-color: var(--info);
  color: var(--info);
}

.btn-icon.btn-outline:hover {
  background: var(--info);
  border-color: var(--info);
  color: var(--white);
}

/* Badge Styles */
.language-badge,
.category-badge,
.position-badge,
.duration-badge,
.price-badge,
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.language-badge {
  background: var(--info);
  color: var(--white);
}

.category-badge {
  background: var(--primary-100);
  color: var(--primary-700);
}

.position-badge {
  background: var(--info);
  color: var(--white);
}

.duration-badge {
  background: var(--gray-100);
  color: var(--gray-700);
}

.price-badge {
  background: var(--success);
  color: var(--white);
  font-weight: 700;
}

.status-badge {
  border-radius: var(--radius);
}

.status-badge.status-active {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.status-badge.status-inactive {
  background: rgba(156, 163, 175, 0.1);
  color: var(--gray-500);
}

.status-badge.status-pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.status-badge.status-confirmed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.status-badge.status-cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
}

/* Contact Links */
.contact-link {
  color: var(--primary-600);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition);
}

.contact-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

/* Service/Reservation Specific */
.service-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2);
  background: var(--gray-50);
  border-radius: var(--radius);
  margin: var(--space-3) 0;
}

.reservation-datetime {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-3);
  background: var(--primary-50);
  border-radius: var(--radius);
  margin-bottom: var(--space-3);
}

.date-value {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--primary-700);
}

.time-value {
  font-size: var(--text-sm);
  color: var(--primary-600);
  font-weight: 500;
}

.service-name {
  color: var(--primary-600);
  font-weight: 500;
}

.employee-name {
  color: var(--info);
  font-weight: 500;
}

/* Utility Classes */
.flex {
  display: flex;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: var(--space-2);
}

.gap-3 {
  gap: var(--space-3);
}

.gap-4 {
  gap: var(--space-4);
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.min-w-0 {
  min-width: 0;
}

.space-y-3 > * + * {
  margin-top: var(--space-3);
}

.mt-1 {
  margin-top: var(--space-1);
}

.mt-2 {
  margin-top: var(--space-2);
}

.mb-2 {
  margin-bottom: var(--space-2);
}

.text-gray-400 {
  color: var(--gray-400);
}

.text-gray-500 {
  color: var(--gray-500);
}

.text-gray-600 {
  color: var(--gray-600);
}

.text-gray-800 {
  color: var(--gray-800);
}

.text-sm {
  font-size: var(--text-sm);
}

.text-xs {
  font-size: var(--text-xs);
}

/* Service-specific styles */
.service-timing {
  margin: var(--space-3) 0;
}

.timing-breakdown {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  font-size: var(--text-xs);
  color: var(--gray-600);
}

.timing-item {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  background: var(--gray-100);
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.timing-item.prep {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.timing-item.main {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.timing-item.cleanup {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
}

/* Employee-specific styles */
.employee-avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-4);
}

.employee-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: var(--text-xl);
  box-shadow: var(--shadow-md);
}

.employee-skills {
  margin: var(--space-4) 0;
}

.skills-header {
  margin-bottom: var(--space-2);
}

.skills-label {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.skill-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}

.skill-badge.more {
  background: var(--gray-100);
  color: var(--gray-600);
  font-style: italic;
}

/* Category-specific styles */
.category-icon-section {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-4);
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-xl);
  box-shadow: var(--shadow-md);
}

.category-services {
  margin: var(--space-4) 0;
}

.services-header {
  margin-bottom: var(--space-2);
}

.services-label {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.services-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2);
  background: var(--gray-50);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
}

.service-item.more {
  font-style: italic;
  color: var(--gray-600);
}

.service-name {
  font-weight: 500;
  color: var(--gray-800);
}

.service-price {
  font-weight: 600;
  color: var(--success);
}

.color-swatch {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-300);
  box-shadow: var(--shadow-sm);
}

/* Form Consistency Improvements */

.form-group {
  margin-bottom: var(--space-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}

.form-group label {
  display: block;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
}

.form-control,
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="number"],
input[type="password"],
input[type="date"],
input[type="time"],
select,
textarea {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  font-size: var(--text-sm);
  transition: all var(--transition);
  background: var(--white);
}

.form-control:focus,
input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  background: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}

/* === BUTTON SYSTEM === */
.btn {
  /* Base button styles */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--button-padding);
  border: 1px solid transparent;
  border-radius: var(--button-border-radius);
  font-family: inherit;
  font-size: var(--text-sm);
  font-weight: var(--button-font-weight);
  line-height: var(--leading-none);
  text-decoration: none;
  text-align: center;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  transition: all var(--transition);

  /* Touch-friendly minimum size */
  min-height: 44px;

  /* Prevent text selection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.btn:focus {
  outline: none;
  box-shadow: var(--form-input-focus-ring);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Variants */
.btn-primary {
  background-color: var(--primary-600);
  color: var(--white);
  border-color: var(--primary-600);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-800);
}

.btn-success {
  background: var(--success);
  color: var(--white);
  border-color: var(--success);
}

.btn-success:hover {
  background: #059669;
  border-color: #059669;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  background: transparent;
  color: var(--gray-600);
  border-color: var(--gray-300);
}

.btn-outline:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-800);
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
  min-height: 52px;
}

/* Additional Button Variants */
.btn-danger {
  background-color: var(--error-500);
  color: var(--white);
  border-color: var(--error-500);
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--error-600);
  border-color: var(--error-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  background-color: var(--warning-500);
  color: var(--white);
  border-color: var(--warning-500);
}

.btn-warning:hover:not(:disabled) {
  background-color: var(--warning-600);
  border-color: var(--warning-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-info {
  background-color: var(--info-500);
  color: var(--white);
  border-color: var(--info-500);
}

.btn-info:hover:not(:disabled) {
  background-color: var(--info-600);
  border-color: var(--info-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Icon Buttons */
.btn-icon {
  padding: var(--space-2);
  min-width: 44px;
  aspect-ratio: 1;
}

.btn-icon.btn-sm {
  padding: var(--space-1);
  min-width: 36px;
}

.btn-icon.btn-lg {
  padding: var(--space-3);
  min-width: 52px;
}

/* Mobile Responsive Buttons */
@media (max-width: 768px) {
  .btn {
    min-height: 48px;
    /* Larger touch targets on mobile */
    padding: var(--space-3) var(--space-4);
  }

  .btn-sm {
    min-height: 40px;
    padding: var(--space-2) var(--space-3);
  }

  .btn-lg {
    min-height: 56px;
    padding: var(--space-4) var(--space-6);
  }
}

/* Enhanced Entity View Styles */
.entity-view {
  max-width: 600px;
  margin: 0 auto;
}

.view-page {
  margin: 0 auto;
  padding: var(--space-6);
}

.view-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 2px solid var(--gray-100);
  gap: var(--space-4);
}

.view-header-content {
  flex: 1;
  min-width: 0;
}

.view-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
  flex-shrink: 0;
  flex-wrap: wrap;
}

/* Mobile responsive view header */
@media (max-width: 768px) {
  .view-page {
    padding: var(--space-4);
  }

  .view-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }

  .view-actions {
    justify-content: flex-start;
    order: -1;
    /* Put actions above content on mobile */
  }

  .view-actions .btn {
    flex: 1;
    min-width: 0;
    justify-content: center;
  }

  .view-title {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .view-title h1 {
    font-size: var(--text-xl);
  }
}

.view-breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
  font-size: var(--text-sm);
}

.breadcrumb-link {
  color: var(--primary-600);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-1);
  transition: color var(--transition);
}

.breadcrumb-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.breadcrumb-separator {
  color: var(--gray-400);
}

.breadcrumb-current {
  color: var(--gray-600);
  font-weight: var(--font-medium);
}

.view-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.view-title h1 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--gray-800);
  margin: 0;
}

.title-content {
  flex: 1;
  min-width: 0;
}

.view-subtitle {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-1);
}

.employee-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  flex-shrink: 0;
}

.view-title-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.view-badges {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.customer-type-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.customer-type-badge.new {
  background: rgba(156, 163, 175, 0.1);
  color: var(--gray-600);
}

.customer-type-badge.regular {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-600);
}

.customer-type-badge.vip {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.view-section {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  border: 1px solid var(--gray-200);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--gray-200);
}

.section-header h2 {
  margin: 0;
  color: var(--gray-800);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.section-content {
  margin: 0;
  overflow: auto;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--space-4);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius);
  text-decoration: none;
  color: var(--gray-700);
  transition: all var(--transition);
  cursor: pointer;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.action-btn:hover {
  background: var(--gray-50);
  border-color: var(--primary-300);
  color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.action-btn i {
  font-size: var(--text-lg);
  color: var(--primary-500);
}

@media (max-width: 768px) {
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--gray-700);
  margin: 0 0 var(--space-4) 0;
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--gray-200);
}

.section-title i {
  color: var(--primary-500);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
}

@media (min-width: 768px) {
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
  border-left: 3px solid var(--primary-200);
}

.info-item .info-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-500);
  flex-shrink: 0;
  margin-top: 2px;
}

.info-item .info-content {
  flex: 1;
  min-width: 0;
}

.info-item .info-label {
  display: block;
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.info-item .info-value {
  display: block;
  font-size: var(--text-sm);
  color: var(--gray-800);
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
}

@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Global Search Results */
.global-search-results {
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
  margin: 0 20px;
}

.search-loading,
.search-no-results,
.search-error {
  padding: 20px;
  text-align: center;
  color: #6c757d;
}

.search-loading i {
  margin-right: 8px;
}

.search-results-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.search-close {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.search-close:hover {
  background: #e9ecef;
  color: #495057;
}

.search-results-list {
  max-height: 320px;
  overflow-y: auto;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-result-item:hover {
  background: #f8f9fa;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #6c757d;
}

.search-result-content {
  flex: 1;
  min-width: 0;
}

.search-result-title {
  font-weight: 500;
  color: #212529;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-result-subtitle {
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-result-description {
  font-size: 0.8125rem;
  color: #868e96;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-result-type {
  font-size: 0.75rem;
  color: #6c757d;
  background: #f1f3f4;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: capitalize;
  margin-left: 8px;
}

.stat-card {
  text-align: center;
  padding: var(--space-4);
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.stat-card .stat-value {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--space-1);
}

.stat-card .stat-label {
  font-size: var(--text-xs);
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.notes-content,
.description-content {
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius);
  border-left: 4px solid var(--primary-500);
}

.notes-content p,
.description-content p {
  margin: 0;
  line-height: 1.6;
  color: var(--gray-700);
}

.timing-breakdown-view {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.timing-breakdown-view .timing-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
  font-weight: 500;
}

.timing-breakdown-view .timing-item.prep {
  border-left: 4px solid var(--warning);
}

.timing-breakdown-view .timing-item.main {
  border-left: 4px solid var(--success);
}

.timing-breakdown-view .timing-item.cleanup {
  border-left: 4px solid var(--primary-500);
}

.text-muted {
  color: var(--gray-500) !important;
}

/* Additional View Components */
.employee-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-right: var(--space-4);
}

.employee-avatar-small {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-xs);
  font-weight: 600;
}

.category-icon-large {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-right: var(--space-4);
}

.position-badge {
  padding: var(--space-1) var(--space-3);
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-600);
  border-radius: var(--radius);
  font-size: var(--text-xs);
  font-weight: 600;
}

.sort-badge {
  padding: var(--space-1) var(--space-3);
  background: rgba(107, 114, 128, 0.1);
  color: var(--gray-600);
  border-radius: var(--radius);
  font-size: var(--text-xs);
  font-weight: 600;
}

.date-badge {
  padding: var(--space-1) var(--space-3);
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
  border-radius: var(--radius);
  font-size: var(--text-xs);
  font-weight: 600;
}

.services-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
  border-left: 3px solid var(--primary-500);
}

.service-name {
  font-weight: 500;
  color: var(--gray-800);
}

.service-price {
  font-weight: 600;
  color: var(--primary-600);
}

.working-hours-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.working-day {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
}

.day-name {
  font-weight: 600;
  color: var(--gray-700);
  min-width: 80px;
}

.day-hours {
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.category-services-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.category-service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
  border-left: 3px solid var(--primary-500);
}

.service-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.service-duration {
  font-size: var(--text-xs);
  color: var(--gray-500);
}

.service-pricing {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-1);
}

.service-status {
  font-size: var(--text-xs);
}

.color-swatch {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: var(--space-2);
  border: 1px solid var(--gray-300);
}

@media (min-width: 768px) {
  .view-title-section {
    flex-direction: row;
    align-items: center;
  }

  .working-day {
    flex-direction: row;
  }

  .category-service-item {
    flex-direction: row;
  }

  .service-info {
    flex-direction: row;
    align-items: center;
    gap: var(--space-4);
  }

  .service-pricing {
    flex-direction: row;
    align-items: center;
    gap: var(--space-3);
  }
}

/* Icon-only action buttons */
.btn-icon {
  background: none !important;
  border: none !important;
  padding: 8px !important;
  width: 36px;
  height: 36px;
  border-radius: 50% !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  color: var(--secondary-color);
  font-size: 14px;
  cursor: pointer;
}

.btn-icon:hover {
  background: var(--light-color) !important;
  color: var(--primary-color);
  transform: scale(1.1);
}

.btn-icon.btn-primary:hover {
  color: var(--primary-color);
  background: rgba(37, 99, 235, 0.1) !important;
}

.btn-icon.btn-success:hover {
  color: var(--success-color);
  background: rgba(16, 185, 129, 0.1) !important;
}

.btn-icon.btn-danger:hover {
  color: var(--danger-color);
  background: rgba(239, 68, 68, 0.1) !important;
}

.btn-icon.btn-outline:hover {
  color: var(--info-color);
  background: rgba(6, 182, 212, 0.1) !important;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.card-title {
  color: var(--dark-color);
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.card-subtitle {
  color: var(--secondary-color);
  font-size: 14px;
  margin: 4px 0 0 0;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-body {
  margin-bottom: 20px;
}

.card-footer {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* === DASHBOARD STYLES === */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* Responsive Dashboard Grid */
@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-8);
  }
}

@media (min-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Dashboard Cards */
.dashboard-grid .card {
  padding: var(--space-6);
  transition: all var(--transition);
}

.dashboard-grid .card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dashboard-grid .card-header {
  margin-bottom: var(--space-5);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--gray-200);
}

.dashboard-grid .card-title {
  color: var(--gray-800);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.dashboard-grid .card-title i {
  color: var(--primary-600);
  font-size: var(--text-lg);
}

/* Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Statistics */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--primary-color);
}

.stat-label {
  color: var(--secondary-color);
  font-size: 0.9rem;
}

/* === DUPLICATE BUTTON STYLES REMOVED === */
/* Button styles are now consolidated in the main button system above */

.btn-sm {
  min-height: 40px;
  padding: 10px 16px;
  font-size: var(--font-size-sm);
}

.btn-lg {
  min-height: 56px;
  padding: 18px 24px;
  font-size: var(--font-size-lg);
}

/* === DUPLICATE BUTTON STYLES REMOVED === */
/* All button variants are consolidated in the main button system above */

/* === DUPLICATE ICON BUTTON STYLES REMOVED === */
/* Icon button styles are consolidated in the main button system above */

/* Forms */
.form-container {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 2rem;
  margin-bottom: 2rem;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h2,
.form-section h3 {
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

/* Form value for view pages - displays data like form inputs */
.form-value {
  padding: 0.5rem 0.75rem;
  background-color: var(--light-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-size: 0.875rem;
  line-height: 1.5;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

/* Employee list styles for view pages */
.employee-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.employee-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--light-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  min-width: 200px;
}

.employee-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.25rem;
}

.employee-position {
  font-size: 0.75rem;
  color: var(--muted-color);
}

/* Working days styles */

.working-days {
  flex-wrap: wrap;
  flex-direction: initial !important;
}

.day-hours {
  flex: 1 1 30%;
  min-width: 30%;
}

/* Working hours styles */
.working-hours-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.working-day {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: var(--light-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.day-name {
  font-weight: 600;
  color: var(--dark-color);
  min-width: 80px;
}

.day-hours {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.time-period {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Services list styles */
.services-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.service-item {
  padding: 0.75rem;
  background: var(--light-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.service-name {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.25rem;
}

.service-details {
  font-size: 0.75rem;
  color: var(--muted-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.toggle-switch input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-switch input:disabled + .toggle-slider {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Toggle with label */
.toggle-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-label {
  font-weight: 500;
  color: var(--dark-color);
}

/* === DUPLICATE FORM STYLES REMOVED === */
/* Form styles are consolidated in the main form system above */

.form-control:disabled {
  background: var(--light-color);
  color: var(--secondary-color);
  cursor: not-allowed;
}

/* Flash Messages */
.flash-message {
  padding: 16px 20px;
  margin-bottom: 24px;
  border-radius: var(--border-radius);
  font-weight: 500;
  border-left: 4px solid;
}

.flash-success {
  background: #d1fae5;
  color: #065f46;
  border-color: var(--success-color);
}

.flash-error {
  background: #fee2e2;
  color: #991b1b;
  border-color: var(--danger-color);
}

.flash-warning {
  background: #fef3c7;
  color: #92400e;
  border-color: var(--warning-color);
}

.flash-info {
  background: #cffafe;
  color: #155e75;
  border-color: var(--info-color);
}

/* Table System - Large Screens Only (1200px+) */
.table-container {
  display: none;
  /* Hidden by default - cards are primary */
}

@media (min-width: 1200px) {
  .table-container {
    display: block;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: var(--space-6);
  }

  .table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-size: var(--text-sm);
  }

  .table th {
    background: var(--gray-50);
    padding: var(--space-3) var(--space-4);
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
    white-space: nowrap;
    font-size: var(--text-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .table td {
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
    color: var(--gray-800);
  }

  .table tbody tr:hover {
    background: var(--gray-50);
  }

  .table tbody tr:last-child td {
    border-bottom: none;
  }

  /* Table sorting */
  .table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
  }

  .table th.sortable:hover {
    background: var(--gray-100);
  }

  .table th.sortable::after {
    content: "";
    position: absolute;
    right: var(--space-2);
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid var(--gray-400);
    opacity: 0.5;
  }

  .table th.sortable.asc::after {
    border-bottom: 4px solid var(--primary-500);
    opacity: 1;
  }

  .table th.sortable.desc::after {
    border-bottom: none;
    border-top: 4px solid var(--primary-500);
    opacity: 1;
  }
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #f1f5f9;
  color: #475569;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.confirmed {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.cancelled {
  background: #fef2f2;
  color: #dc2626;
}

.status-badge.completed {
  background: #dcfce7;
  color: #166534;
}

/* Entity Grid System - Card-First Approach */
.entity-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

/* Tablet: 2 columns */
@media (min-width: 768px) {
  .entity-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-5);
  }
}

/* Desktop: 3 columns */
@media (min-width: 1024px) {
  .entity-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-6);
  }
}

/* Large Desktop: 4 columns or table option */
@media (min-width: 1400px) {
  .entity-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-6);
  }
}

/* Entity Card - Primary Component */
.entity-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition);
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 280px;
}

.entity-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--gray-300);
}

.entity-card--selected {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 1px var(--primary-500), var(--shadow-md);
}

/* Entity card status indicators */
.entity-card--active {
  border-left: 4px solid var(--success);
}

.entity-card--inactive {
  border-left: 4px solid var(--gray-400);
  opacity: 0.8;
}

.entity-card--warning {
  border-left: 4px solid var(--warning);
}

.entity-card--error {
  border-left: 4px solid var(--error);
}

.entity-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--primary-color);
}

/* Mobile-First Entity Card Header */
.entity-card-header {
  padding: 16px;
  position: relative;
  background: var(--light-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.entity-select {
  margin-top: 4px;
  flex-shrink: 0;
}

.entity-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 6px;
}

.entity-status-indicator.active {
  background: var(--success-color);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.entity-status-indicator.warning {
  background: var(--warning-color);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.entity-status-indicator.inactive {
  background: var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(100, 116, 139, 0.2);
}

.entity-title-section {
  flex: 1;
  min-width: 0;
}

.entity-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--dark-color);
  margin: 0 0 4px 0;
  line-height: 1.3;
  word-break: break-word;
}

.entity-subtitle {
  font-size: 14px;
  color: var(--secondary-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* Mobile-First Entity Card Body */
.entity-card-body {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.entity-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  background: var(--light-color);
  margin: 15px -20px -20px -20px;
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Mobile-Optimized Info Rows */
.entity-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: var(--light-color);
  border-radius: var(--border-radius);
  border: 1px solid transparent;
  transition: var(--transition);
}

.info-row:hover {
  border-color: var(--border-color);
  background: white;
}

.info-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 14px;
  flex-shrink: 0;
  background: white;
  border-radius: 50%;
  border: 1px solid var(--border-color);
}

.info-icon.email {
  color: #ea4335;
}

.info-icon.phone {
  color: #34a853;
}

.info-icon.language {
  color: #4285f4;
}

.info-icon.description {
  color: var(--secondary-color);
}

.info-value {
  flex: 1;
  color: var(--dark-color);
  font-weight: 500;
  text-decoration: none;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
}

.info-value:hover {
  color: var(--primary-color);
}

/* Contact links styling */
.contact-link {
  color: var(--dark-color);
  text-decoration: none;
  transition: var(--transition);
}

.contact-link:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* Mobile-Optimized Stats Section */
.entity-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  gap: 1px;
  background: var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin: 16px 0;
}

.stat-item {
  background: white;
  padding: 12px 8px;
  text-align: center;
  transition: var(--transition);
}

.stat-item:hover {
  background: var(--light-color);
}

.stat-value {
  display: block;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--primary-color);
  line-height: 1.1;
  margin-bottom: 4px;
}

.stat-label {
  display: block;
  font-size: 11px;
  color: var(--secondary-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* Mobile-Optimized Action Buttons */
.entity-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding: 16px;
  background: var(--light-color);
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.btn-icon {
  background: white !important;
  border: 1px solid var(--border-color) !important;
  padding: 12px !important;
  width: 44px;
  height: 44px;
  border-radius: var(--border-radius) !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  color: var(--secondary-color);
  font-size: 16px;
  cursor: pointer;
  box-shadow: var(--shadow);
}

.btn-icon:hover {
  background: var(--primary-color) !important;
  color: white;
  border-color: var(--primary-color) !important;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-icon.btn-primary:hover {
  background: var(--primary-color) !important;
}

.btn-icon.btn-success:hover {
  background: var(--success-color) !important;
  border-color: var(--success-color) !important;
}

.btn-icon.btn-danger:hover {
  background: var(--danger-color) !important;
  border-color: var(--danger-color) !important;
}

.btn-icon.btn-outline:hover {
  background: var(--info-color) !important;
  border-color: var(--info-color) !important;
}

/* Action buttons container */
.action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: flex-end;
}

/* Mobile-Optimized Entity Footer */
.entity-footer {
  padding: 12px 16px;
  background: var(--light-color);
  border-top: 1px solid var(--border-color);
  font-size: 12px;
  color: var(--secondary-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-top: auto;
}

.entity-footer::before {
  content: "";
  width: 6px;
  height: 6px;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.6;
  flex-shrink: 0;
}

/* Entity-Specific Card Styling */
.customer-card {
  border-left: 4px solid var(--primary-color);
}

.service-card {
  border-left: 4px solid var(--success-color);
}

.employee-card {
  border-left: 4px solid var(--info-color);
}

.category-card {
  border-left: 4px solid var(--warning-color);
}

.reservation-card {
  border-left: 4px solid var(--secondary-color);
}

.reservation-card.confirmed {
  border-left-color: var(--success-color);
}

.reservation-card.pending {
  border-left-color: var(--warning-color);
}

.reservation-card.cancelled {
  border-left-color: var(--danger-color);
}

/* Status Badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-confirmed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.status-active {
  background: rgba(37, 99, 235, 0.1);
  color: var(--primary-color);
}

.status-inactive {
  background: rgba(100, 116, 139, 0.1);
  color: var(--secondary-color);
}

/* Enhanced Table Styling */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.table th {
  background: var(--light-color);
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--dark-color);
  border-bottom: 2px solid var(--border-color);
  font-size: 14px;
  white-space: nowrap;
}

.table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
  font-size: 14px;
}

.table tr:hover {
  background: var(--light-color);
}

/* Clickable table rows */
.table-row-clickable {
  cursor: pointer;
  transition: all var(--transition);
}

.table-row-clickable:hover {
  background: var(--primary-50) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-row-clickable:active {
  transform: translateY(0);
  background: var(--primary-100) !important;
}

/* Table Controls and Column Selector */
.table-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: var(--space-4);
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.column-selector {
  position: relative;
  display: inline-block;
}

.column-selector-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  color: var(--gray-700);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition);
}

.column-selector-toggle:hover {
  border-color: var(--primary-500);
  color: var(--primary-600);
}

.column-selector-toggle.active {
  border-color: var(--primary-500);
  color: var(--primary-600);
  box-shadow: 0 0 0 1px var(--primary-500);
}

.column-selector-toggle .fa-chevron-down {
  transition: transform var(--transition);
}

.column-selector-toggle.active .fa-chevron-down {
  transform: rotate(180deg);
}

.column-selector-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  min-width: 220px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all var(--transition);
}

.column-selector-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.column-selector-header {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--gray-100);
  background: var(--gray-50);
}

.column-selector-header:first-child {
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}

.column-selector-header strong {
  display: block;
  color: var(--gray-800);
  font-size: var(--text-sm);
  font-weight: 600;
}

.column-selector-header small {
  color: var(--gray-500);
  font-size: var(--text-xs);
}

.column-option {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  cursor: pointer;
  transition: background-color var(--transition);
}

.column-option:hover {
  background: var(--gray-50);
}

.column-option.essential {
  opacity: 0.7;
  cursor: not-allowed;
}

.column-option input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.column-option.essential input[type="checkbox"] {
  cursor: not-allowed;
}

.column-option label {
  flex: 1;
  margin: 0;
  font-size: var(--text-sm);
  color: var(--gray-700);
  cursor: pointer;
}

.column-option.essential label {
  cursor: not-allowed;
}

/* Enhanced Dropdown Component for View Actions */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0;
  background: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition);
}

.dropdown-toggle:hover {
  border-color: var(--primary-500);
  color: var(--primary-600);
  background: var(--primary-50);
}

.dropdown-toggle:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-width: 200px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all var(--transition);
  padding: var(--space-2) 0;
}

.dropdown.open .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  color: var(--gray-700);
  text-decoration: none;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-size: var(--text-sm);
  transition: all var(--transition);
}

.dropdown-item:hover {
  background: var(--gray-50);
  color: var(--gray-900);
}

.dropdown-item.disabled {
  color: var(--gray-400);
  cursor: not-allowed;
  opacity: 0.6;
}

.dropdown-item.disabled:hover {
  background: transparent;
  color: var(--gray-400);
}

.dropdown-item.text-danger {
  color: var(--error-600);
}

.dropdown-item.text-danger:hover {
  background: var(--error-50);
  color: var(--error-700);
}

.dropdown-item i {
  width: 16px;
  text-align: center;
  flex-shrink: 0;
}

.dropdown-divider {
  height: 1px;
  background: var(--gray-200);
  margin: var(--space-2) 0;
}

/* View Actions Container */
.view-actions .dropdown-toggle {
  border: 1px solid var(--gray-300);
  background: var(--white);
}

.view-actions .dropdown-toggle:hover {
  border-color: var(--gray-400);
  background: var(--gray-50);
}

/* Mobile responsive dropdown */
@media (max-width: 768px) {
  .dropdown-menu {
    right: 0;
    left: auto;
    min-width: 180px;
  }

  .dropdown-item {
    padding: var(--space-3);
    font-size: var(--text-sm);
  }
}

/* Add Card Content Styling */
.add-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition);
}

.add-card:hover .add-card-content {
  transform: translateY(-2px);
}

.add-card-content i {
  color: var(--gray-400);
  margin-bottom: var(--space-4);
  transition: color var(--transition);
}

.add-card:hover .add-card-content i {
  color: var(--primary-500);
}

.add-card-content h3 {
  color: var(--gray-600);
  font-size: var(--text-lg);
  font-weight: 500;
  margin: 0;
  transition: color var(--transition);
}

.add-card:hover .add-card-content h3 {
  color: var(--primary-600);
}

.table tr:last-child td {
  border-bottom: none;
}

/* Table cell styling */
.contact-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.contact-link:hover {
  text-decoration: underline;
}

.language-badge {
  background: var(--info-color);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

.stat-highlight {
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  min-width: 24px;
  text-align: center;
}

.price-highlight {
  color: var(--success-color);
  font-weight: 700;
  font-size: 14px;
}

.date-text {
  color: var(--secondary-color);
  font-size: 13px;
}

/* Additional table cell styling */
.category-badge {
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  display: inline-block;
}

.description-text {
  color: var(--dark-color);
  font-size: 13px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.duration-badge {
  background: var(--info-color);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
}

.popularity-badge {
  background: var(--warning-color);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
}

.position-badge {
  background: var(--info-color);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.schedule-info {
  color: var(--secondary-color);
  font-size: 13px;
  font-weight: 500;
}

.performance-badge {
  background: var(--success-color);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
}

.icon-cell {
  text-align: center;
  font-size: 16px;
}

.booking-id {
  font-family: monospace;
  background: var(--light-color);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.service-name {
  color: var(--primary-color);
  font-weight: 500;
}

.employee-name {
  color: var(--info-color);
  font-weight: 500;
}

.date-highlight {
  background: var(--light-color);
  padding: 4px 8px;
  border-radius: 8px;
  font-weight: 500;
  color: var(--dark-color);
}

.time-highlight {
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 12px;
}

.payment-status {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
}

.payment-status:contains("Paid") {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.payment-status:contains("Pending") {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.payment-status:contains("Failed") {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* Sort button styling */
.sort-btn {
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  padding: 2px 4px;
  margin-left: 8px;
  border-radius: 4px;
  transition: var(--transition);
  font-size: 12px;
}

.sort-btn:hover {
  background: var(--light-color);
  color: var(--primary-color);
}

.sort-btn i {
  transition: var(--transition);
}

th[data-sort="asc"] .sort-btn {
  color: var(--primary-color);
}

th[data-sort="desc"] .sort-btn {
  color: var(--primary-color);
}

/* Toolbar button styling */
.toolbar-btn {
  background: white;
  border: 1px solid var(--border-color);
  padding: 8px 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.toolbar-btn:hover {
  background: var(--light-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.toolbar-btn i {
  font-size: 12px;
}

.actions-cell {
  white-space: nowrap;
}

.actions-cell .action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.actions-cell .btn-icon {
  width: 32px;
  height: 32px;
  font-size: 12px;
}

/* Enhanced Table Responsiveness */
@media (max-width: 768px) {
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: var(--border-radius);
    margin: 0 calc(-1 * var(--container-padding));
  }

  .table {
    min-width: 700px;
    font-size: var(--font-size-xs);
  }

  .table th,
  .table td {
    padding: 8px 10px;
    font-size: var(--font-size-xs);
  }

  .table th {
    font-weight: 600;
  }

  .actions-cell .btn-icon {
    width: 32px;
    height: 32px;
    font-size: 12px;
    min-width: 32px;
  }
}

/* Very small screens - optimize for card view */
@media (max-width: 480px) {
  .table-container {
    margin: 0 calc(-1 * var(--container-padding));
  }

  .table {
    min-width: 600px;
  }

  .table th,
  .table td {
    padding: 6px 8px;
    font-size: 11px;
  }

  .actions-cell .btn-icon {
    width: 28px;
    height: 28px;
    font-size: 10px;
    min-width: 28px;
  }
}

/* Mobile-First Responsive Design */
@media (max-width: 480px) {
  .entity-card {
    margin: 0 calc(-1 * var(--container-padding));
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
  }

  .entity-card-header {
    padding: 16px;
  }

  .entity-card-body {
    padding: 16px;
    gap: 14px;
  }

  .entity-title {
    font-size: var(--font-size-lg);
    line-height: 1.3;
  }

  .entity-subtitle {
    font-size: var(--font-size-sm);
  }

  .info-row {
    padding: 8px 12px;
    gap: 12px;
    border-radius: var(--border-radius);
  }

  .info-icon {
    width: 24px;
    height: 24px;
    font-size: 14px;
    flex-shrink: 0;
  }

  .info-value {
    font-size: var(--font-size-sm);
    line-height: 1.4;
  }

  .entity-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 2px;
  }

  .stat-item {
    padding: 12px 8px;
    text-align: center;
  }

  .stat-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
  }

  .stat-label {
    font-size: var(--font-size-xs);
    margin-top: 4px;
  }

  .entity-actions {
    padding: 16px;
    gap: 12px;
    justify-content: center;
  }

  .btn-icon {
    width: 48px;
    height: 48px;
    font-size: 16px;
    min-width: 48px;
  }

  .entity-footer {
    padding: 8px 12px;
    font-size: 11px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .entity-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .entity-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Add Card Animation */
.entity-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.entity-card.loading {
  opacity: 0.6;
  pointer-events: none;
}

.entity-card.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* View transition loading states */
.view-loading {
  position: relative;
  opacity: 0.7;
  pointer-events: none;
}

.view-loading::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid var(--gray-300);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 11;
}

/* Smooth transitions for view changes */
.entity-grid {
  transition: opacity 0.2s ease-in-out;
}

.table-container {
  transition: opacity 0.2s ease-in-out;
}

.entity-grid.transitioning,
.table-container.transitioning {
  opacity: 0;
}

/* Global loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.loading-overlay .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-300);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-overlay .loading-text {
  margin-top: var(--space-4);
  color: var(--gray-600);
  font-size: var(--text-sm);
  font-weight: 500;
}

/* Modern Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-right: 12px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: 0.3s ease;
  border-radius: 24px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s ease;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
  background-color: var(--primary-600);
  box-shadow: inset 0 1px 3px rgba(37, 99, 235, 0.3);
}

.toggle-switch input:focus + .toggle-slider {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-switch input:disabled + .toggle-slider {
  opacity: 0.6;
  cursor: not-allowed;
}

.toggle-switch input:disabled + .toggle-slider:before {
  cursor: not-allowed;
}

/* Toggle group styling */
.toggle-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.toggle-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--gray-100);
}

.toggle-item:last-child {
  border-bottom: none;
}

.toggle-label {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-right: 16px;
}

.toggle-label-text {
  font-weight: 500;
  color: var(--gray-900);
  margin-bottom: 4px;
}

.toggle-label-description {
  font-size: var(--text-sm);
  color: var(--gray-600);
  line-height: 1.4;
}

/* Hover effects */
.toggle-item:hover .toggle-slider {
  background-color: var(--gray-400);
}

.toggle-item:hover .toggle-switch input:checked + .toggle-slider {
  background-color: var(--primary-700);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.entity-card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
}

.entity-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--dark-color);
  margin: 0 0 8px 0;
}

.entity-subtitle {
  color: var(--secondary-color);
  font-size: 14px;
  margin: 0;
}

.entity-card-body {
  padding: 20px 24px;
}

.entity-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-label {
  color: var(--secondary-color);
  font-weight: 500;
}

.info-value {
  color: var(--dark-color);
  font-weight: 500;
}

.entity-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.entity-footer {
  padding: 16px 24px;
  background: var(--light-color);
  border-top: 1px solid var(--border-color);
  font-size: 12px;
  color: var(--secondary-color);
}

/* Translation Management Styles */
.translation-filters {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 600;
  color: #495057;
  margin: 0;
  white-space: nowrap;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
  color: #495057;
  font-size: 14px;
  min-width: 200px;
  cursor: pointer;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-group select:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filter-group select:hover {
  border-color: #adb5bd;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.translation-form {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.translations-container {
  max-height: 70vh;
  overflow-y: auto;
}

.translations-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
  background: #e9ecef;
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.translation-column {
  background: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 2px solid #dee2e6;
}

.translation-column h3 {
  margin: 0 0 5px 0;
  color: #495057;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.translation-column small {
  color: #6c757d;
  font-size: 12px;
}

.translation-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
  background: #e9ecef;
  border-bottom: 1px solid #e9ecef;
}

.translation-cell {
  background: white;
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.translation-key {
  font-family: "Courier New", monospace;
  font-size: 12px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  display: inline-block;
  max-width: fit-content;
}

.translation-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  min-height: 38px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.translation-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.translation-input.textarea {
  min-height: 60px;
  font-family: inherit;
}

.translation-actions {
  background: #f8f9fa;
  padding: 20px;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.translation-stats {
  color: #6c757d;
  font-size: 14px;
}

/* Responsive adjustments for translations */
@media (max-width: 768px) {
  .translation-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    justify-content: space-between;
  }

  .filter-group select {
    min-width: auto;
    flex: 1;
  }

  .translations-header,
  .translation-row {
    grid-template-columns: 1fr;
  }

  .translation-column {
    border-bottom: 1px solid #dee2e6;
  }

  .translation-column:last-child {
    border-bottom: 2px solid #dee2e6;
  }
}

/* Simplified Responsive Design - Card-First Approach */

/* Toolbar Responsive Behavior */
.toolbar-right {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

@media (min-width: 768px) {
  .toolbar-right {
    flex-direction: row;
    align-items: center;
    gap: var(--space-4);
  }
}

/* Responsive behavior is now handled by the individual component styles above */
/* Entity grid responsive behavior is defined in the Entity Grid System section */
/* Dashboard grid responsive behavior is defined in the Dashboard Grid section */
/* View toggle responsive behavior is defined in the View Toggle section */

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.action-buttons .btn {
  padding: 12px 20px;
}

/* Page Headers */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
}

.page-header-content h1 {
  color: var(--dark-color);
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: var(--secondary-color);
  font-size: 16px;
  margin: 0;
}

.page-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-color);
  background: var(--light-color);
  border-radius: var(--border-radius);
  margin: 20px 0;
}

.empty-state p {
  margin-bottom: 16px;
  font-size: 16px;
}

.empty-state i {
  font-size: 2rem;
  margin-bottom: 16px;
  opacity: 0.5;
  display: block;
}

/* Contact and interactive links */
.contact-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

.contact-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* Text utilities */
.text-muted {
  color: var(--gray-600) !important;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-warning {
  color: var(--warning-color);
}

/* Font sizes */
.fa-4x {
  font-size: 2rem;
}

/* Login Page Styles */
.login-page {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-hover) 100%
  );
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  padding: 40px 30px 30px;
  background: var(--light-color);
  border-bottom: 1px solid var(--border-color);
}

.login-header h1 {
  color: var(--primary-color);
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.login-header p {
  color: var(--secondary-color);
  margin: 0;
  font-size: 16px;
}

.login-form {
  padding: 30px;
}

.login-form .form-group {
  margin-bottom: 20px;
}

.login-form .form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--dark-color);
  font-weight: 500;
  font-size: 14px;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-group i {
  position: absolute;
  left: 12px;
  color: var(--secondary-color);
  z-index: 2;
  width: 16px;
  height: 16px;
}

.input-group input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
  background: white;
}

.input-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: var(--transition);
  z-index: 2;
}

.password-toggle:hover {
  color: var(--primary-color);
  background: var(--light-color);
}

.btn-block {
  width: 100%;
  justify-content: center;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 10px;
}

.login-footer {
  text-align: center;
  padding: 20px 30px 30px;
  background: var(--light-color);
  border-top: 1px solid var(--border-color);
}

.login-footer p {
  color: var(--secondary-color);
  margin: 0 0 4px 0;
  font-size: 14px;
}

.login-footer small {
  color: var(--secondary-color);
  font-size: 12px;
  opacity: 0.8;
}

.error-message {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  padding: 12px 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.error-message i {
  color: #dc2626;
  flex-shrink: 0;
}

/* Additional utility classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 2rem;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: 0.5rem;
}

.mt-2 {
  margin-top: 1rem;
}

.mt-3 {
  margin-top: 1.5rem;
}

.mt-4 {
  margin-top: 2rem;
}

.d-flex {
  display: flex;
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center {
  align-items: center;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 1rem;
}

.gap-4 {
  gap: 1.5rem;
}

/* === MODAL SYSTEM === */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition);
  padding: var(--space-4);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--white);
  border-radius: var(--modal-border-radius);
  box-shadow: var(--modal-shadow);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(-20px);
  transition: transform var(--transition);
}

.modal.show .modal-content {
  transform: scale(1) translateY(0);
}

.modal-header {
  padding: var(--space-6) var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--gray-800);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius);
  transition: all var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--gray-600);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-4) var(--space-6) var(--space-6);
  border-top: 1px solid var(--gray-200);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* Modal Form Styles */
.modal-form {
  margin: 0;
}

.modal-form .form-group {
  margin-bottom: var(--space-4);
}

.modal-form .form-actions {
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--gray-200);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* Mobile Modal Adjustments */
@media (max-width: 768px) {
  .modal {
    padding: var(--space-2);
    align-items: flex-start;
    padding-top: var(--space-8);
  }

  .modal-content {
    max-width: 100%;
    max-height: calc(100vh - var(--space-16));
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: var(--space-4);
    padding-right: var(--space-4);
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-footer .btn {
    width: 100%;
  }
}

/* === FORM-SPECIFIC STYLES === */
/* Customer Form Styles */
.customer-stats,
.customer-actions {
  margin: var(--space-6) 0;
  padding: var(--space-5);
  background: var(--gray-50);
  border-radius: var(--radius);
  border: 1px solid var(--gray-200);
}

.customer-stats h4,
.customer-actions h4 {
  margin: 0 0 var(--space-4) 0;
  color: var(--gray-600);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
}

.customer-dates {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.date-item {
  padding: var(--space-2) var(--space-3);
  background: var(--white);
  border-radius: var(--radius);
  border: 1px solid var(--gray-200);
  font-size: var(--text-sm);
  color: var(--gray-700);
}

.date-item strong {
  color: var(--gray-800);
  font-weight: var(--font-semibold);
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: var(--space-3);
  padding-top: var(--space-6);
  border-top: 1px solid var(--gray-200);
  margin-top: var(--space-6);
}

/* Mobile Form Adjustments */
@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
    gap: var(--space-2);
  }

  .customer-stats .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* === PAGE LAYOUT STYLES === */
/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
}

.page-header-left h1 {
  margin: 0 0 var(--space-2) 0;
  color: var(--gray-800);
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
}

.page-header-right {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.breadcrumb {
  font-size: var(--text-sm);
  color: var(--gray-500);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.breadcrumb a {
  color: var(--primary-600);
  text-decoration: none;
  transition: color var(--transition);
}

.breadcrumb a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.breadcrumb-separator {
  color: var(--gray-400);
}

/* Content Card */
.content-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--space-8);
  margin-bottom: var(--space-8);
  border: 1px solid var(--gray-200);
}

/* Form Sections */
.form-section {
  margin-bottom: var(--space-8);
}

.form-section-title {
  margin: 0 0 var(--space-6) 0;
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-800);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
}

/* Entity Form */
.entity-form {
  margin: 0;
}

.entity-form .form-group {
  margin-bottom: var(--space-6);
}

/* Alert Styles */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius);
  margin-bottom: var(--space-6);
  border: 1px solid;
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
}

.alert-error {
  background-color: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-700);
}

.alert-success {
  background-color: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-700);
}

.alert-warning {
  background-color: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-700);
}

.alert-info {
  background-color: var(--info-50);
  border-color: var(--info-200);
  color: var(--info-700);
}

/* Mobile Page Adjustments */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .page-header-left h1 {
    font-size: var(--text-2xl);
  }

  .page-header-right {
    justify-content: flex-start;
  }

  .content-card {
    padding: var(--space-4);
  }

  .breadcrumb {
    flex-wrap: wrap;
  }
}

/* === CUSTOMER-SPECIFIC STYLES === */
.customer-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  flex-shrink: 0;
}

.customer-stats-section {
  background: var(--gray-50);
  padding: var(--space-4);
  border-radius: var(--radius);
  margin: var(--space-3) 0;
  border: 1px solid var(--gray-200);
}

.customer-stats-section .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
  padding: var(--space-2) 0;
}

.customer-stats-section .stat-item:last-child {
  margin-bottom: 0;
}

.customer-stats-section .stat-label {
  font-weight: var(--font-medium);
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.customer-stats-section .stat-value {
  font-weight: var(--font-semibold);
  color: var(--gray-800);
  font-size: var(--text-sm);
}

/* Card Footer */
.card-footer {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  margin: var(--space-4) calc(-1 * var(--space-4)) calc(-1 * var(--space-4));
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* Pagination */
.pagination-container {
  margin-top: var(--space-8);
  display: flex;
  justify-content: center;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-16) var(--space-8);
  color: var(--gray-500);
}

.empty-state i {
  margin-bottom: var(--space-6);
  color: var(--gray-400);
}

.empty-state h3 {
  margin-bottom: var(--space-4);
  color: var(--gray-600);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
}

.empty-state p {
  margin-bottom: var(--space-6);
  color: var(--gray-500);
  font-size: var(--text-base);
}

/* Add Card */
.add-card {
  border: 2px dashed var(--gray-300);
  background: var(--gray-50);
  transition: all var(--transition);
  cursor: pointer;
}

.add-card:hover {
  border-color: var(--primary-400);
  background: var(--primary-50);
  transform: translateY(-1px);
}

.add-card:hover i {
  color: var(--primary-600) !important;
}

.add-card:hover h3 {
  color: var(--primary-700) !important;
}

.add-card-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  flex-direction: column;
  gap: 16px;
}

.add-card-content h3 {
  margin: 0;
}

/* Mobile Customer View Adjustments */
@media (max-width: 768px) {
  .card-footer {
    flex-direction: column;
    gap: var(--space-2);
  }

  .customer-stats-section .stat-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
}

/* === EMPLOYEE-SPECIFIC STYLES === */
.employee-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: var(--font-bold);
  font-size: var(--text-sm);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.services-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-1);
  margin-top: var(--space-1);
}

.service-badge {
  padding: var(--space-1) var(--space-2);
  background: var(--primary-600);
  color: var(--white);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  white-space: nowrap;
}

.service-badge:hover {
  background: var(--primary-700);
}

/* Employee Working Hours */
.working-hours {
  font-size: var(--text-sm);
  color: var(--gray-600);
  margin-top: var(--space-2);
}

.working-hours-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  margin-top: var(--space-2);
}

.working-hours-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  background: var(--gray-50);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
}

.working-hours-day {
  font-weight: var(--font-medium);
  color: var(--gray-700);
}

.working-hours-time {
  color: var(--gray-600);
}

/* Employee Status */
.employee-status {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.employee-status--active {
  background: var(--success-100);
  color: var(--success-700);
}

.employee-status--inactive {
  background: var(--gray-100);
  color: var(--gray-600);
}

.employee-status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* === MOBILE OPTIMIZATIONS === */
/* Touch-friendly interactions */
@media (max-width: 768px) {
  /* Larger touch targets */
  .btn,
  .nav-link,
  .entity-card,
  .form-control,
  input,
  select,
  textarea,
  .table-row-clickable {
    min-height: 48px;
  }

  /* Allow natural touch behavior on table rows */
  .table-row-clickable {
    /* Let browser handle touch events naturally */
  }

  /* Improve touch scrolling - let browser handle naturally */
  .entity-grid,
  .main-content,
  .content-area {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Allow natural text selection and touch behavior */
  .entity-card {
    /* Remove user-select restrictions to allow natural touch behavior */
  }

  /* Better spacing for mobile */
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }

  .toolbar-left,
  .toolbar-right {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .search-bar {
    min-width: auto;
    width: 100%;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }

  .filter-label {
    font-weight: var(--font-semibold);
  }

  /* Mobile entity grid */
  .entity-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
    /* Natural mobile scrolling */
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Mobile stats grid */
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }

  /* Mobile dashboard */
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  /* Mobile modal adjustments */
  .modal {
    padding: var(--space-2);
    align-items: flex-start;
    padding-top: var(--space-4);
  }

  .modal-content {
    max-height: calc(100vh - var(--space-8));
    margin-top: var(--space-4);
  }

  /* Mobile navigation */
  .nav-link {
    padding: var(--space-4) var(--space-5);
    font-size: var(--text-base);
  }

  /* Mobile form improvements */
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .form-actions {
    flex-direction: column;
    gap: var(--space-3);
  }

  .form-actions .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Extra small devices (phones in portrait) */
@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-3);
  }

  .main {
    padding: var(--space-4);
  }

  .page-header {
    margin-bottom: var(--space-6);
  }

  .page-header-left h1 {
    font-size: var(--text-xl);
  }

  .content-card {
    padding: var(--space-4);
    margin-bottom: var(--space-6);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .entity-stats {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .action-buttons {
    flex-direction: column;
    gap: var(--space-2);
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: var(--radius);
    border-right-width: 1px;
    border-bottom-width: 0;
  }

  .btn-group .btn:not(:last-child) {
    border-bottom-width: 0;
  }

  .btn-group .btn:last-child {
    border-bottom-width: 1px;
  }
}

/* Landscape phone adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .sidebar {
    width: 240px;
  }

  .main-content {
    margin-left: 240px;
    width: calc(100% - 240px);
  }

  .entity-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .entity-status-indicator,
  .color-swatch {
    border-width: 0.5px;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .card:hover,
  .btn:hover,
  .entity-card:hover {
    transform: none;
  }
}

/* Settings */
.tab-content {
  background-color: #fff;
  border-radius: 16px;
}

/* === DASHBOARD SPECIFIC COMPONENTS === */

/* Dashboard container for proper spacing */
.dashboard-container {
  margin: 0 auto;
  padding: 0;
}

/* Reservation Lists */
.reservation-list {
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--gray-300) transparent;
}

.reservation-list::-webkit-scrollbar {
  width: 6px;
}

.reservation-list::-webkit-scrollbar-track {
  background: transparent;
}

.reservation-list::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 10px;
}

.reservation-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.15s ease;
}

.reservation-item:last-child {
  border-bottom: none;
}

.reservation-item:hover {
  background: var(--light-color);
  margin: 0 -16px;
  padding: 12px 16px;
  border-radius: 8px;
}

.reservation-time,
.reservation-date {
  width: 80px;
  text-align: center;
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.9rem;
  background: var(--primary-50);
  padding: 8px;
  border-radius: 6px;
  border: 1px solid var(--primary-200);
  flex-shrink: 0;
}

.reservation-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  width: 90px;
}

.reservation-date small {
  font-size: 0.75rem;
  color: var(--secondary-color);
  font-weight: normal;
}

.reservation-details {
  flex: 1;
  margin-left: 16px;
  min-width: 0;
}

.reservation-customer {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 4px;
  font-size: 0.875rem;
}

.reservation-service {
  color: var(--secondary-color);
  font-size: 0.875rem;
  line-height: 1.25;
}

.reservation-status {
  flex-shrink: 0;
  margin-left: 16px;
}

/* Status badges for reservations */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: 1px solid;
}

.status-badge::before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.status-badge.confirmed,
.status-badge.active {
  background: var(--success-100);
  color: var(--success-700);
  border-color: var(--success-600);
}

.status-badge.pending {
  background: var(--warning-100);
  color: var(--warning-700);
  border-color: var(--warning-600);
}

.status-badge.cancelled,
.status-badge.inactive {
  background: var(--error-100);
  color: var(--error-700);
  border-color: var(--error-600);
}

.status-badge.completed {
  background: var(--info-100);
  color: var(--info-700);
  border-color: var(--info-600);
}

/* Enhanced action buttons for dashboard */
.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 12px;
}

@media (max-width: 768px) {
  .action-buttons {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.action-buttons .btn {
  justify-content: center;
  padding: 12px 16px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Mobile responsive adjustments for dashboard */
@media (max-width: 768px) {
  .reservation-time,
  .reservation-date {
    width: 70px;
    font-size: 0.75rem;
    padding: 6px;
  }

  .reservation-details {
    margin-left: 12px;
  }

  .reservation-status {
    margin-left: 8px;
  }

  .reservation-item {
    padding: 16px 0;
  }

  .reservation-item:hover {
    margin: 0 -12px;
    padding: 16px 12px;
  }
}

@media (max-width: 480px) {
  .reservation-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px 0;
  }

  .reservation-time,
  .reservation-date {
    width: auto;
    text-align: left;
  }

  .reservation-details {
    margin-left: 0;
  }

  .reservation-status {
    margin-left: 0;
    align-self: flex-start;
  }

  .reservation-item:hover {
    margin: 0;
    padding: 12px;
  }
}

/* === CALENDAR SPECIFIC COMPONENTS === */

/* Calendar Navigation */
.calendar-navigation {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.calendar-nav-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

@media (max-width: 768px) {
  .calendar-nav-controls {
    flex-direction: column;
    gap: 16px;
  }
}

.calendar-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-800);
  text-align: center;
  margin: 0;
}

.calendar-nav-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  min-width: 120px;
  justify-content: center;
}

@media (max-width: 768px) {
  .calendar-nav-btn .nav-text {
    display: none;
  }

  .calendar-nav-btn {
    min-width: auto;
    width: 48px;
    height: 48px;
    padding: 0;
  }
}

/* Calendar Grid */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.calendar-day-header {
  padding: 12px;
  text-align: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--gray-700);
  background: var(--light-color);
  border-bottom: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.calendar-day-header:last-child {
  border-right: none;
}

.calendar-day {
  min-height: 120px;
  border-right: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
  padding: 8px;
  position: relative;
  background: white;
  transition: background-color 0.15s ease;
  cursor: pointer;
}

.calendar-day:last-child {
  border-right: none;
}

.calendar-day:hover {
  background: var(--light-color);
}

.calendar-day.today {
  background: var(--primary-50);
  border-color: var(--primary-200);
}

.calendar-day.other-month {
  background: var(--gray-100);
  color: var(--gray-400);
}

.calendar-day.has-reservations {
  background: linear-gradient(135deg, #d1fae5 0%, #ecfdf5 100%);
}

.calendar-day-number {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.calendar-day.today .calendar-day-number {
  color: var(--primary-color);
  font-weight: 700;
}

.calendar-day.other-month .calendar-day-number {
  color: var(--gray-400);
}

.calendar-day-count {
  background: var(--primary-color);
  color: white;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.calendar-reservations {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 80px;
  overflow: hidden;
}

.calendar-reservation {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 6px;
  font-size: 0.75rem;
  line-height: 1.25;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  gap: 1em;
  align-items: center;
  flex-direction: initial !important;
  justify-content: start !important;
}

.calendar-reservation:hover {
  background: var(--light-color);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  z-index: 10;
  white-space: normal;
  height: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.calendar-reservation--confirmed {
  border-left: 3px solid var(--success-color);
  background: var(--success-100);
}

.calendar-reservation--pending {
  border-left: 3px solid var(--warning-color);
  background: var(--warning-100);
}

.calendar-reservation--cancelled {
  border-left: 3px solid var(--danger-color);
  background: var(--error-100);
  opacity: 0.7;
}

.calendar-reservation-time {
  font-weight: 600;
  color: var(--gray-800);
}

.calendar-reservation-customer {
  color: var(--gray-600);
  font-weight: 500;
}

.calendar-reservation-service {
  color: var(--gray-500);
}

/* Mobile Calendar Adjustments */
@media (max-width: 768px) {
  .calendar-day {
    min-height: 80px;
    padding: 4px;
  }

  .calendar-day-number {
    font-size: 0.875rem;
    margin-bottom: 4px;
  }

  .calendar-day-count {
    font-size: 10px;
    padding: 2px 4px;
    min-width: 16px;
  }

  .calendar-reservations {
    max-height: 40px;
    gap: 2px;
  }

  .calendar-reservation {
    padding: 2px 4px;
    font-size: 10px;
  }

  .calendar-day-header {
    padding: 8px 4px;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .calendar-day {
    min-height: 60px;
  }

  .calendar-reservations {
    max-height: 30px;
  }

  .calendar-reservation {
    padding: 1px 3px;
    font-size: 8px;
  }
}

/* === COMPACT VIEW PAGE SYSTEM === */

/* View Page Container */
.view-page {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* View Page Header */
.view-header {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-6);
}

.view-header-content {
  flex: 1;
  min-width: 0;
}

.view-breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
  font-size: var(--text-sm);
  color: var(--gray-500);
}

.breadcrumb-link {
  color: var(--primary-600);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-1);
  transition: color var(--transition);
}

.breadcrumb-link:hover {
  color: var(--primary-700);
}

.breadcrumb-separator {
  color: var(--gray-400);
}

.breadcrumb-current {
  color: var(--gray-700);
  font-weight: var(--font-medium);
}

.view-title {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
}

.view-title h1 {
  margin: 0;
  color: var(--gray-800);
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
}

.view-subtitle {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-top: var(--space-2);
  flex-wrap: wrap;
}

.view-actions {
  display: flex;
  gap: var(--space-3);
  align-items: flex-start;
  flex-shrink: 0;
}

/* Category Icon Large */
.category-icon-large {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: var(--text-2xl);
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.title-content {
  flex: 1;
}

.english-name {
  color: var(--gray-600);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
}

.service-count {
  color: var(--gray-500);
  font-size: var(--text-sm);
}

/* View Content */
.view-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.view-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

/* View Sections */
.view-section {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.section-header {
  padding: var(--space-5) var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-3);
}

.section-header h2 {
  margin: 0;
  color: var(--gray-800);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.section-header h2 i {
  color: var(--primary-600);
  font-size: var(--text-base);
}

.section-content {
  padding: var(--space-6);
}

/* Compact Info Grid */
.info-grid {
  display: grid;
  gap: var(--space-4);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-600);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.info-item span {
  color: var(--gray-800);
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
}

/* Contact Links */
.contact-link {
  color: var(--primary-600);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  transition: color var(--transition);
}

.contact-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.contact-link::before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: var(--text-sm);
  color: var(--gray-400);
}

a[href^="mailto:"].contact-link::before {
  content: "\f0e0";
  /* envelope icon */
}

a[href^="tel:"].contact-link::before {
  content: "\f095";
  /* phone icon */
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
}

.stat-item {
  text-align: center;
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius);
  border: 1px solid var(--gray-200);
}

.stat-value {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  line-height: 1;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
  font-weight: var(--font-medium);
}

/* Color Preview */
.color-preview {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius);
  color: var(--white);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.color-preview::before {
  content: "";
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: currentColor;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* Category Badge */
.category-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius);
  color: var(--white);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Position Badge */
.position-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border: 1px solid var(--primary-200);
}

/* Price Display */
.price {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--success-600);
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--space-3);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  color: var(--gray-700);
  text-decoration: none;
  transition: all var(--transition);
  cursor: pointer;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.action-btn:hover {
  background: var(--gray-50);
  border-color: var(--primary-300);
  color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.action-btn i {
  font-size: var(--text-lg);
  color: var(--primary-600);
}

/* Employee List */
.employee-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.employee-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
  border: 1px solid var(--gray-200);
}

.employee-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: var(--font-bold);
  font-size: var(--text-sm);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-weight: var(--font-semibold);
  color: var(--gray-800);
  margin-bottom: var(--space-1);
}

.employee-position {
  font-size: var(--text-sm);
  color: var(--gray-600);
}

/* Working Hours */
.working-day {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
  margin-bottom: var(--space-2);
}

.day-name {
  font-weight: var(--font-medium);
  color: var(--gray-700);
  text-transform: capitalize;
}

.day-hours {
  display: flex;
  gap: var(--space-2);
}

.time-period {
  font-size: var(--text-sm);
  color: var(--gray-600);
  padding: var(--space-1) var(--space-2);
  background: var(--white);
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-200);
}

/* Service Item */
.service-item {
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--space-2);
}

.service-name {
  font-weight: var(--font-semibold);
  color: var(--gray-800);
  margin-bottom: var(--space-1);
}

.service-details {
  font-size: var(--text-sm);
  color: var(--gray-600);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex-wrap: wrap;
}

/* Compact Information Items with Icons */
.info-item-with-icon {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius);
  border: 1px solid var(--gray-200);
}

.info-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-100);
  color: var(--primary-600);
  border-radius: var(--radius);
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.info-value {
  color: var(--gray-800);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
}

/* Date Information */
.date-info {
  color: var(--gray-600);
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .view-header {
    flex-direction: column;
    gap: var(--space-4);
    align-items: stretch;
  }

  .view-actions {
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  .view-title {
    flex-direction: column;
    gap: var(--space-3);
  }

  .view-title h1 {
    font-size: var(--text-2xl);
  }

  .view-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header {
    padding: var(--space-4);
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }

  .section-content {
    padding: var(--space-4);
  }

  .category-icon-large {
    width: 48px;
    height: 48px;
    font-size: var(--text-xl);
  }
}

@media (max-width: 480px) {
  .view-header {
    padding: var(--space-4);
  }

  .section-header,
  .section-content {
    padding: var(--space-3);
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .employee-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
}

/* Status Enhancement */
.status-pending {
  background: var(--warning-100);
  color: var(--warning-700);
  border-color: var(--warning-600);
}

.status-confirmed,
.status-active {
  background: var(--success-100);
  color: var(--success-700);
  border-color: var(--success-600);
}

.status-completed {
  background: var(--info-100);
  color: var(--info-700);
  border-color: var(--info-600);
}

.status-cancelled,
.status-inactive {
  background: var(--error-100);
  color: var(--error-700);
  border-color: var(--error-600);
}

.status-no_show {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-600);
}

/* Split Toolbar Styles */
.main-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
}

.main-toolbar .toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
}

.main-toolbar .toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.filters-section {
  background: var(--gray-50);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--space-6);
  overflow: hidden;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: var(--gray-100);
  border-bottom: 1px solid var(--border-color);
}

.filters-header h3 {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--gray-700);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.filters-content {
  padding: var(--space-4);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.filters-content .filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.filters-content .filter-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  margin-bottom: var(--space-1);
}

.filters-content .form-control {
  font-size: var(--text-sm);
}

/* Responsive filters */
@media (max-width: 768px) {
  .main-toolbar {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }

  .main-toolbar .toolbar-left,
  .main-toolbar .toolbar-right {
    justify-content: center;
  }

  .filters-content {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .filters-header {
    flex-direction: column;
    gap: var(--space-2);
    align-items: stretch;
  }
}

/* Search Button Styles */
.search-bar {
  position: relative;
  display: flex;
  align-items: center;
}

.search-bar input {
  padding-right: 45px;
}

.search-btn {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.search-btn:hover {
  background: var(--gray-100);
  color: var(--primary-color);
}

.search-btn:active {
  background: var(--gray-200);
}

/* Remove old search icon styles */
.search-icon {
  display: none;
}

/* Consolidated Info Box Styles */
.consolidated-info-box {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--space-3);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.consolidated-info-box.compact {
  padding: var(--space-2);
  gap: var(--space-1);
}

.info-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-3);
  align-items: start;
}

.consolidated-info-box.compact .info-row {
  gap: var(--space-2);
}

.compact-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
  padding-bottom: var(--space-1);
  border-bottom: 1px solid var(--border-color);
  font-weight: var(--font-semibold);
  color: var(--gray-700);
  font-size: var(--text-sm);
}

.compact-header i {
  color: var(--primary-600);
}

.info-row.full-width {
  grid-template-columns: 1fr;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  min-height: 24px;
}

.info-item .info-icon {
  color: var(--primary-600);
  font-size: var(--text-sm);
  margin-top: 2px;
  flex-shrink: 0;
  width: 16px;
}

.info-item .info-label {
  font-weight: var(--font-medium);
  color: var(--gray-700);
  font-size: var(--text-sm);
  flex-shrink: 0;
  min-width: fit-content;
}

.info-item .info-value {
  color: var(--gray-900);
  font-size: var(--text-sm);
  flex: 1;
}

.info-item .info-value.description {
  margin-top: 0;
  line-height: 1.5;
}

.info-item .info-value.price {
  font-weight: var(--font-semibold);
  color: var(--primary-600);
}

.info-item .category-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: var(--radius);
  color: white;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .info-row {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .consolidated-info-box {
    padding: var(--space-3);
    gap: var(--space-2);
  }
}

/* Searchable Dropdown Styles */
.searchable-dropdown {
  position: relative;
  width: 100%;
}

.searchable-dropdown .input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.searchable-dropdown .searchable-input {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  padding-right: 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--text-sm);
  background: var(--white);
  transition: all 0.2s ease;
  text-align: left;
  color: var(--gray-900);
}

.searchable-dropdown .searchable-input::placeholder {
  color: var(--gray-500);
  text-align: left;
}

.searchable-dropdown .searchable-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.searchable-dropdown .dropdown-arrow {
  position: absolute;
  right: var(--space-2);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-500);
  cursor: pointer;
  padding: var(--space-1);
  transition: transform 0.2s ease;
}

.searchable-dropdown.open .dropdown-arrow {
  transform: translateY(-50%) rotate(180deg);
}

.searchable-dropdown .dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--white);
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1050;
  display: none;
}

.searchable-dropdown.open .dropdown-list {
  display: block;
}

.searchable-dropdown .dropdown-item {
  padding: var(--space-2) var(--space-3);
  cursor: pointer;
  font-size: var(--text-sm);
  border-bottom: 1px solid var(--gray-100);
  transition: background-color 0.15s ease;
  text-align: left;
  color: var(--gray-900);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.searchable-dropdown .dropdown-item:last-child {
  border-bottom: none;
}

.searchable-dropdown .dropdown-item:hover,
.searchable-dropdown .dropdown-item.highlighted {
  background-color: var(--primary-50);
  color: var(--primary-700);
}

.searchable-dropdown .dropdown-item.no-results {
  color: var(--gray-500);
  font-style: italic;
  cursor: default;
}

.searchable-dropdown .dropdown-item.no-results:hover {
  background-color: transparent;
  color: var(--gray-500);
}

.searchable-dropdown .dropdown-item.more-indicator {
  color: var(--gray-600);
  font-size: var(--text-xs);
  background-color: var(--gray-50);
  cursor: default;
  text-align: center;
}

.searchable-dropdown .dropdown-item.more-indicator:hover {
  background-color: var(--gray-50);
  color: var(--gray-600);
}

/* Responsive adjustments for searchable dropdown */
@media (max-width: 768px) {
  .searchable-dropdown .dropdown-list {
    max-height: 150px;
  }

  .searchable-dropdown .dropdown-item {
    padding: var(--space-3);
    font-size: var(--text-base);
  }
}

/* Clickable stat items */
.stat-item-clickable {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.stat-item-clickable:hover {
  background-color: var(--gray-50);
  border-radius: var(--border-radius);
}

.stat-arrow {
  font-size: 0.75rem;
  margin-left: var(--space-1);
  transition: transform 0.2s ease;
  color: var(--gray-500);
}

.stat-item-clickable:hover .stat-arrow {
  color: var(--primary-600);
}
