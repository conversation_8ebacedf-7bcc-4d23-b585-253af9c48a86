/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue",
    Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
}

.business-name {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.business-subtitle {
  font-size: 1.2rem;
  color: #718096;
  font-weight: 400;
}

/* Language Selector Styles */
.language-selector {
  position: absolute;
  top: 20px;
  right: 20px;
}

.language-dropdown {
  position: relative;
}

.language-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4a5568;
  transition: all 0.2s ease;
  min-width: 120px;
}

.language-btn:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.language-options {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 140px;
  z-index: 1000;
  display: none;
  overflow: hidden;
}

.language-options.show {
  display: block;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  text-decoration: none;
  color: #4a5568;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.language-option:hover {
  background-color: #f7fafc;
}

.language-option.active {
  background-color: #e6fffa;
  color: #2d3748;
  font-weight: 500;
}

.language-option .flag {
  font-size: 16px;
}

/* Progress Bar Styles */
.progress-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.progress-bar {
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  margin-bottom: 20px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  width: 14.28%;
  transition: width 0.3s ease;
  border-radius: 3px;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  z-index: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e2e8f0;
  color: #a0aec0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.step-label {
  font-size: 0.75rem;
  color: #a0aec0;
  font-weight: 500;
  text-align: center;
  transition: color 0.3s ease;
}

.progress-step.active .step-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.1);
}

.progress-step.active .step-label {
  color: #667eea;
}

.progress-step.completed .step-number {
  background: #48bb78;
  color: white;
}

.progress-step.completed .step-label {
  color: #48bb78;
}

/* Main Content Styles */
.main-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.step-content {
  min-height: 400px;
  position: relative;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 15px;
  color: #718096;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Error Message Styles */
.error-message {
  background: #fed7d7;
  color: #c53030;
  border: 1px solid #feb2b2;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

.error-close {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 18px;
  color: #c53030;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Step Content Styles */
.step-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 10px;
  text-align: center;
}

.step-subtitle {
  font-size: 1.125rem;
  color: #718096;
  margin-bottom: 40px;
  text-align: center;
}

/* Category Grid */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.category-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.category-card.selected {
  border-color: #667eea;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.1) 0%,
    rgba(118, 75, 162, 0.1) 100%
  );
}

.category-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
}

.category-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 10px;
}

.category-description {
  color: #718096;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Service Grid */
.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.service-card {
  background: white;
  border-radius: 16px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.service-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-card.selected {
  border-color: #667eea;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.1) 0%,
    rgba(118, 75, 162, 0.1) 100%
  );
}

.service-card.disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
  opacity: 0.6;
}

.service-card.disabled:hover {
  transform: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.service-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2d3748;
}

.service-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #667eea;
}

.service-status {
  font-size: 0.875rem;
  font-weight: 600;
  color: #e53e3e;
  background: #fed7d7;
  padding: 4px 8px;
  border-radius: 6px;
  margin-left: 10px;
}

.service-description {
  color: #718096;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 15px;
}

.service-duration {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #718096;
  font-size: 0.875rem;
}

/* Calendar Styles */
.calendar-container {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 40px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.calendar-month {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
}

.calendar-nav {
  display: flex;
  gap: 10px;
}

.calendar-nav-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #667eea;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.calendar-nav-btn:hover {
  background: rgba(102, 126, 234, 0.1);
}

.calendar-nav-btn:disabled {
  color: #a0aec0;
  cursor: not-allowed;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

.calendar-day-header {
  text-align: center;
  padding: 10px 5px;
  font-weight: 600;
  color: #718096;
  font-size: 0.875rem;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
}

.calendar-day:hover {
  background: rgba(102, 126, 234, 0.1);
}

.calendar-day.disabled {
  color: #a0aec0;
  cursor: not-allowed;
}

.calendar-day.disabled:hover {
  background: transparent;
}

.calendar-day.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.calendar-day.today {
  background: #e2e8f0;
  font-weight: 600;
}

.calendar-day.today.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.calendar-day.future-limit {
  background: #f0f8ff;
  color: #6b7280;
  border: 1px dashed #cbd5e0;
  position: relative;
}

.calendar-day.future-limit:hover {
  background: #f0f8ff;
}

.future-limit-indicator {
  position: absolute;
  bottom: 2px;
  left: 2px;
  right: 2px;
  font-size: 0.6rem;
  color: #6b7280;
  text-align: center;
  line-height: 1;
}

/* Time Slots */
.time-slots-container {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 40px;
}

.time-slots-header {
  text-align: center;
  margin-bottom: 25px;
}

.selected-date {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 5px;
}

.selected-service {
  color: #718096;
  font-size: 0.875rem;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.time-slot {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.time-slot:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.time-slot.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.time-slot.disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
  border-color: #e2e8f0;
}

.time-slot.disabled:hover {
  border-color: #e2e8f0;
  background: #f7fafc;
}

/* Enhanced time slot visual feedback */
.time-slot.past-time {
  position: relative;
  background: #f7fafc;
  color: #a0aec0;
}

.time-slot.past-time .deletion-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 40%,
    #cbd5e0 40%,
    #cbd5e0 60%,
    transparent 60%
  );
  pointer-events: none;
}

.time-slot.booked {
  background: #fed7cc;
  border-color: #f6ad55;
  color: #c05621;
}

.time-slot.booked .booked-indicator {
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 2px;
}

.time-slot.booked:hover {
  background: #fed7cc;
  border-color: #f6ad55;
}

.time-slot.employee-unavailable {
  background: #f7fafc;
  color: #a0aec0;
  border-color: #e2e8f0;
}

.time-slot.employee-unavailable .employee-unavailable-indicator {
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 2px;
}

.time-slot.duration-conflict {
  background: #fef5e7;
  border-color: #f6ad55;
  color: #c05621;
}

.time-slot.duration-conflict .duration-conflict-indicator {
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 2px;
}

.time-slot.duration-conflict:hover {
  background: #fef5e7;
  border-color: #f6ad55;
}

.time-slot.insufficient-time {
  background: #fffbeb;
  border-color: #f6e05e;
  color: #b7791f;
}

.time-slot.insufficient-time .insufficient-time-indicator {
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 2px;
}

.time-slot.insufficient-time:hover {
  background: #fffbeb;
  border-color: #f6e05e;
}

.time-slot.employee-unavailable:hover {
  background: #f7fafc;
  border-color: #e2e8f0;
}

/* Interval break styling */
.interval-break {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  margin: 15px 0;
  opacity: 0.7;
}

.interval-break-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, transparent, #ccc, transparent);
}

.interval-break-text {
  padding: 0 15px;
  font-size: 12px;
  color: #666;
  font-style: italic;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 4px 12px;
}

/* Form Styles */
.form-container {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 40px;
}

.form-group {
  margin-bottom: 25px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2d3748;
}

.form-input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: #f56565;
}

.form-error {
  color: #f56565;
  font-size: 0.875rem;
  margin-top: 5px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

/* Verification Styles */
.verification-container {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
}

.verification-icon {
  font-size: 4rem;
  color: #667eea;
  margin-bottom: 20px;
}

.verification-message {
  font-size: 1.125rem;
  color: #718096;
  margin-bottom: 30px;
  line-height: 1.6;
}

.verification-code {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
}

.verification-digit {
  width: 50px;
  height: 50px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  transition: border-color 0.3s ease;
}

.verification-digit:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.verification-digit.error {
  border-color: #f56565;
  background-color: #fed7d7;
}

.verification-error {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 15px;
  margin-bottom: 15px;
  padding: 12px 16px;
  background-color: #fed7d7;
  border: 1px solid #f56565;
  border-radius: 8px;
  color: #c53030;
  font-size: 0.875rem;
  font-weight: 500;
}

.resend-code {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.resend-code:hover {
  color: #764ba2;
}

.resend-timer {
  color: #718096;
  font-size: 0.875rem;
  margin-top: 10px;
}

/* Confirmation Styles */
.confirmation-container {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
}

.confirmation-icon {
  font-size: 4rem;
  color: #48bb78;
  margin-bottom: 20px;
}

.confirmation-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 10px;
}

.confirmation-message {
  font-size: 1.125rem;
  color: #718096;
  margin-bottom: 40px;
  line-height: 1.6;
}

.booking-summary {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 40px;
  text-align: left;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #e2e8f0;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-weight: 600;
  color: #2d3748;
}

.summary-value {
  color: #718096;
}

.summary-price {
  font-weight: 700;
  color: #667eea;
  font-size: 1.125rem;
}

.booking-id {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px;
  border-radius: 12px;
  font-family: "Courier New", monospace;
  font-size: 1.125rem;
  font-weight: 600;
  letter-spacing: 1px;
  margin-bottom: 20px;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 15px 30px;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
  min-width: 120px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
}

.btn-danger {
  background: #f56565;
  color: white;
}

.btn-danger:hover {
  background: #e53e3e;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Navigation Styles */
.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
}

.navigation .btn {
  min-width: 140px;
}

/* Footer Styles */
.footer {
  margin-top: 40px;
  padding: 20px;
  text-align: center;
}

.footer-content {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.footer-contact {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
}

.footer-contact a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-contact a:hover {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header {
    padding: 20px;
  }

  .business-name {
    font-size: 2rem;
  }

  .main-content {
    padding: 25px;
  }

  .progress-container {
    padding: 20px;
  }

  .progress-steps {
    flex-wrap: wrap;
    gap: 15px;
  }

  .category-grid,
  .service-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .navigation {
    flex-direction: column;
    gap: 15px;
  }

  .navigation .btn {
    width: 100%;
  }

  .footer-content {
    flex-direction: column;
    gap: 15px;
  }

  .verification-code {
    gap: 8px;
  }

  .verification-digit {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .step-title {
    font-size: 1.5rem;
  }

  .step-subtitle {
    font-size: 1rem;
  }

  .category-card,
  .service-card {
    padding: 20px;
  }

  .calendar-container,
  .time-slots-container,
  .form-container {
    padding: 20px;
  }

  .time-slots-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .verification-digit {
    width: 35px;
    height: 35px;
    font-size: 1.125rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-in-out;
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-30 {
  margin-top: 30px;
}

.hidden {
  display: none;
}

.visible {
  display: block;
}

/* Employee Selection Styles */

/* Employee Map */
.employee-map-container {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.employee-map-title {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 15px;
  text-align: center;
}

.employee-map-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
  max-width: 600px;
  margin: 0 auto;
}

/* Responsive grid for employee map */
@media (min-width: 768px) {
  .employee-map-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    max-width: 800px;
  }
}

@media (min-width: 1024px) {
  .employee-map-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.employee-map-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 12px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  background: white;
}

.employee-map-item:hover:not(.unavailable) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.employee-map-item.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
}

.employee-map-item.unavailable {
  opacity: 0.5;
  cursor: not-allowed;
}

.employee-map-item.unavailable:hover {
  transform: none;
  box-shadow: none;
  border-color: transparent;
}

.employee-avatar-small {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 6px;
  transition: all 0.2s ease;
}

.employee-avatar-small.auto-assign-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-size: 14px;
}

.employee-name-small {
  font-size: 13px;
  color: #4a5568;
  line-height: 1.2;
  font-weight: 500;
}

.employee-map-item.selected .employee-name-small {
  color: #667eea;
  font-weight: 600;
}

/* Manual Selection Time Slots */
.time-slot.manual-selection {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-slot.manual-selection:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Ensure manual selection slots inherit the same disabled styling as auto selection */
.time-slot.manual-selection.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.time-slot.manual-selection.disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Employee filtered slots (greyed out when specific employee selected) */
.time-slot.manual-selection.employee-filtered {
  opacity: 0.4;
  background: #f8f9fa;
}

.time-slot.manual-selection.employee-filtered:hover {
  transform: none;
  box-shadow: none;
}

/* Employee filter loading spinner */
.employee-filter-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.employee-filter-spinner i {
  font-size: 18px;
  color: #667eea;
}

/* Special days calendar styling */
.calendar-day.special-day-closed {
  background: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.calendar-day.special-day-custom {
  background: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

.special-day-indicator {
  font-size: 10px;
  font-weight: bold;
  margin-top: 2px;
  text-transform: uppercase;
}

.special-day-note {
  font-size: 9px;
  margin-top: 1px;
  opacity: 0.8;
  line-height: 1.1;
}

.time-slot-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.time-text {
  font-weight: 600;
}

.employee-dots {
  display: flex;
  gap: 3px;
  margin: 0 8px;
}

.employee-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.expand-arrow {
  font-size: 12px;
  color: #6c757d;
  transition: transform 0.2s ease;
}

.time-slot.manual-selection.expanded .expand-arrow {
  transform: rotate(180deg);
}

/* Employee List */
.employee-list {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #e9ecef;
}

.employee-option {
  display: flex;
  align-items: center;
  padding: 8px;
  margin: 4px 0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #f8f9fa;
}

.employee-option:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.employee-option.selected {
  background: #d4edda;
  border: 1px solid #c3e6cb;
}

.employee-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.employee-details {
  flex: 1;
}

.employee-name {
  font-weight: 600;
  color: #212529;
  font-size: 14px;
  line-height: 1.2;
}

.employee-position {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.2;
}

/* Selected state for time slots */
.time-slot.manual-selection.selected {
  background: #d4edda;
  border-color: #c3e6cb;
}

/* Disabled state adjustments */
.time-slot.manual-selection.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.time-slot.manual-selection.disabled:hover {
  transform: none;
  box-shadow: none;
}

.slot-reason {
  font-size: 11px;
  color: #6c757d;
  font-style: italic;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .employee-map-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .employee-dots {
    flex-wrap: wrap;
    max-width: 60px;
  }

  .employee-option {
    padding: 6px;
  }

  .employee-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
    margin-right: 8px;
  }

  .employee-name {
    font-size: 13px;
  }

  .employee-position {
    font-size: 11px;
  }
}

/* No employees message */
.no-employees {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
}
